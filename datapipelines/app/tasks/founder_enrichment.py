"""
Modern Founder Enrichment Task for TractionX Data Pipeline Service.

This module provides a clean, production-ready implementation for processing
PDL enrichment data into normalized database tables. Designed for scalability,
maintainability, and future engineer happiness.
"""

import asyncio
from datetime import date, datetime, timezone
from typing import Any, Dict, List, NamedTuple, Optional
from uuid import NAMESPACE_DNS, uuid4, uuid5

from app.configs import get_logger
from app.models.founder import (
    FounderEducation,
    FounderExperience,
    FounderProfile,
    FounderRecord,
)
from app.storage.rds_storage import RDSStorage
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class JobContext(NamedTuple):
    """Job context for founder enrichment processing."""

    job_id: str
    founder_id: str
    company_id: str
    org_id: str
    founder_name: str
    linkedin_url: str


class PDLDataProcessor:
    """
    Comprehensive processor for PDL enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorage, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.PDLDataProcessor")

    async def process_pdl_payload(
        self, payload: Dict[str, Any], s3_raw_data_key: str
    ) -> Dict[str, Any]:
        """
        Process a complete PDL payload and store in normalized schema.

        Args:
            payload: Complete PDL payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload.get("enrichment_data", {})
            metadata = payload.get("metadata", {})

            # Validate required fields
            validation_result = self._validate_pdl_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "founder_id": metadata.get("founder_id"),
                }

            # Generate deterministic UUID
            founder_uuid = self._generate_founder_uuid(enrichment_data)

            # Clean and extract data
            cleaned_data = self._clean_enrichment_data(
                enrichment_data, metadata, founder_uuid, s3_raw_data_key
            )

            # Store in database with transaction
            await self._store_founder_data(cleaned_data)

            self.logger.info(
                f"Successfully processed PDL data for founder {founder_uuid}"
            )

            return {
                "success": True,
                "founder_uuid": founder_uuid,
                "founder_id": metadata.get("founder_id"),
                "records_created": {
                    "experiences": len(cleaned_data["experiences"]),
                    "education": len(cleaned_data["education"]),
                    "skills": len(cleaned_data["skills"]),
                    "profiles": len(cleaned_data["profiles"]),
                },
            }

        except Exception as e:
            error_msg = f"PDL processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "founder_id": metadata.get("founder_id"),
            }

    def _validate_pdl_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate required fields in PDL data."""
        errors = []

        # Check metadata
        required_metadata = ["founder_id", "company_id", "org_id"]
        for field in required_metadata:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check enrichment data
        full_name = enrichment_data.get("full_name", "").strip()
        linkedin_url = enrichment_data.get("linkedin_url", "").strip()

        if not full_name:
            errors.append("Missing or empty full_name")

        if not linkedin_url:
            # Try to get from profiles
            profiles = enrichment_data.get("profiles", [])
            linkedin_profiles = [p for p in profiles if p.get("network") == "linkedin"]
            if not linkedin_profiles:
                errors.append("Missing LinkedIn URL in both direct field and profiles")

        return {"valid": len(errors) == 0, "errors": errors}

    def _generate_founder_uuid(self, enrichment_data: Dict[str, Any]) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL."""
        full_name = enrichment_data.get("full_name", "").strip().lower()
        linkedin_url = enrichment_data.get("linkedin_url", "").strip().lower()

        # Fallback to profiles if direct LinkedIn URL not available
        if not linkedin_url:
            profiles = enrichment_data.get("profiles", [])
            linkedin_profiles = [p for p in profiles if p.get("network") == "linkedin"]
            if linkedin_profiles:
                linkedin_url = linkedin_profiles[0].get("url", "").strip().lower()

        # Create deterministic string for UUID generation
        uuid_string = f"{full_name}|{linkedin_url}"

        # Generate UUIDv5 using DNS namespace
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    def _clean_enrichment_data(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and structure enrichment data for database storage."""

        # Clean basic founder info
        founder_record = self._create_founder_record(
            enrichment_data, metadata, founder_uuid, s3_raw_data_key
        )

        # Extract and clean related data
        experiences = self._extract_experiences(enrichment_data)
        education = self._extract_education(enrichment_data)
        skills = self._extract_skills(enrichment_data)
        profiles = self._extract_profiles(enrichment_data)

        return {
            "founder": founder_record,
            "experiences": experiences,
            "education": education,
            "skills": skills,
            "profiles": profiles,
        }

    def _create_founder_record(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> FounderRecord:
        """Create a clean FounderRecord from PDL data."""

        # Clean name fields
        full_name = self._clean_string(enrichment_data.get("full_name"))
        first_name = self._clean_string(enrichment_data.get("first_name"))
        last_name = self._clean_string(enrichment_data.get("last_name"))

        # Clean job info
        current_job_title = self._clean_string(enrichment_data.get("job_title"))
        current_job_company = self._clean_string(
            enrichment_data.get("job_company_name")
        )

        # Clean URLs
        linkedin_url = self._clean_url(enrichment_data.get("linkedin_url"))
        github_url = self._clean_url(enrichment_data.get("github_url"))

        # Clean location
        location_country = self._clean_string(enrichment_data.get("location_country"))

        # Parse enrichment date
        enrichment_date = None
        if enrichment_data.get("enrichment_date"):
            try:
                enrichment_date = datetime.fromisoformat(
                    enrichment_data["enrichment_date"].replace("Z", "+00:00")
                )
            except (ValueError, AttributeError):
                pass

        # Calculate confidence score
        confidence_score = metadata.get("confidence_score", 1.0)
        if enrichment_data.get("likelihood"):
            confidence_score = float(enrichment_data["likelihood"]) / 10.0

        now = datetime.now(timezone.utc)

        return FounderRecord(
            id=founder_uuid,
            founder_id=metadata["founder_id"],
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            current_job_title=current_job_title,
            current_job_company=current_job_company,
            linkedin_url=linkedin_url,
            github_url=github_url,
            location_country=location_country,
            org_id=metadata["org_id"],
            company_id=metadata["company_id"],
            source="pdl",
            confidence_score=confidence_score,
            enrichment_date=enrichment_date,
            s3_raw_data_key=s3_raw_data_key,
            created_at=now,
            updated_at=now,
        )

    def _extract_experiences(
        self, enrichment_data: Dict[str, Any]
    ) -> List[FounderExperience]:
        """Extract and clean experience records."""
        experiences = []
        experience_list = enrichment_data.get("experience", [])

        for exp_data in experience_list:
            if not isinstance(exp_data, dict):
                continue

            company_info = exp_data.get("company", {})

            # Handle both string and dict title formats
            title = exp_data.get("title")
            if isinstance(title, dict):
                title = title.get("name")

            experience = FounderExperience(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                company_name=self._clean_string(company_info.get("name")),
                title=self._clean_string(title),
                industry=self._clean_string(company_info.get("industry")),
                company_size=self._clean_string(company_info.get("size")),
                start_date=self._parse_date(exp_data.get("start_date")),
                end_date=self._parse_date(exp_data.get("end_date")),
                is_primary=exp_data.get("is_primary"),
                location=self._extract_location_string(exp_data.get("location")),
            )
            experiences.append(experience)

        return experiences

    def _extract_education(
        self, enrichment_data: Dict[str, Any]
    ) -> List[FounderEducation]:
        """Extract and clean education records."""
        education_records = []
        education_list = enrichment_data.get("education", [])

        for edu_data in education_list:
            if not isinstance(edu_data, dict):
                continue

            school_info = edu_data.get("school", {})
            school_name = self._clean_string(school_info.get("name"))

            # Skip entries without school name
            if not school_name:
                continue

            # Clean degree and major lists - filter out None values
            degrees = [
                d for d in edu_data.get("degrees", []) if d and self._clean_string(d)
            ]
            majors = [
                m for m in edu_data.get("majors", []) if m and self._clean_string(m)
            ]

            education = FounderEducation(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                school_name=school_name,
                degrees=degrees,
                majors=majors,
                start_date=self._parse_date(edu_data.get("start_date")),
                end_date=self._parse_date(edu_data.get("end_date")),
                location=self._extract_location_string(school_info.get("location")),
            )
            education_records.append(education)

        return education_records

    def _extract_skills(self, enrichment_data: Dict[str, Any]) -> List[str]:
        """Extract and clean skills list."""
        skills = enrichment_data.get("skills", [])
        if not isinstance(skills, list):
            return []

        # Clean and deduplicate skills
        cleaned_skills = set()
        for skill in skills:
            if isinstance(skill, str):
                cleaned_skill = self._clean_string(skill)
                if (
                    cleaned_skill and len(cleaned_skill) > 1
                ):  # Skip single character skills
                    cleaned_skills.add(cleaned_skill.lower())

        return sorted(list(cleaned_skills))

    def _extract_profiles(
        self, enrichment_data: Dict[str, Any]
    ) -> List[FounderProfile]:
        """Extract and clean social profiles."""
        profiles = []
        profiles_list = enrichment_data.get("profiles", [])

        # Track URLs to avoid duplicates
        seen_urls = set()

        for profile_data in profiles_list:
            if not isinstance(profile_data, dict):
                continue

            network = self._clean_string(profile_data.get("network"))
            url = self._clean_url(profile_data.get("url"))

            if network and url and url not in seen_urls:
                profile = FounderProfile(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    network=network,
                    url=url,
                )
                profiles.append(profile)
                seen_urls.add(url)

        return profiles

    async def _store_founder_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store founder data in database with transaction."""
        founder_record = cleaned_data["founder"]
        experiences = cleaned_data["experiences"]
        education = cleaned_data["education"]
        skills = cleaned_data["skills"]
        profiles = cleaned_data["profiles"]

        # Use the new RDS method for transactional upsert
        await self.rds.upsert_founder_with_relations(
            founder_data=founder_record.model_dump(),
            experiences=[exp.model_dump() for exp in experiences],
            education=[
                {
                    "id": edu.id,
                    "founder_id": "",  # Will be set during storage
                    "school_name": edu.school_name,
                    "degrees": edu.degrees,  # Keep as list
                    "majors": edu.majors,  # Keep as list
                    "start_date": edu.start_date,
                    "end_date": edu.end_date,
                    "location": edu.location,
                }
                for edu in education
            ],
            skills=skills,
            profiles=[
                {
                    "id": prof.id,
                    "founder_id": "",  # Will be set during storage
                    "network": prof.network,
                    "url": prof.url,
                }
                for prof in profiles
            ],
        )

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error for debugging."""
        try:
            error_record = {
                "founder_id": payload.get("metadata", {}).get("founder_id"),
                "company_id": payload.get("metadata", {}).get("company_id"),
                "org_id": payload.get("metadata", {}).get("org_id"),
                "error_message": error_msg,
                "s3_raw_data_key": s3_key,
                "payload_preview": str(payload)[
                    :1000
                ],  # First 1000 chars for debugging
                "created_at": datetime.now(timezone.utc),
            }

            # Store in a dedicated error table (create if needed)
            await self._ensure_error_table_exists()
            await self.rds.insert("founder_processing_errors", error_record)

        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    async def _ensure_error_table_exists(self) -> None:
        """Ensure the error tracking table exists."""
        # Drop the table if it exists to ensure correct schema
        try:
            await self.rds.execute_query(
                "DROP TABLE IF EXISTS founder_processing_errors"
            )
        except Exception as e:
            self.logger.warning(f"Could not drop error table: {e}")

        error_table_schema = {
            "id": "SERIAL PRIMARY KEY",
            "founder_id": "VARCHAR(255)",
            "company_id": "VARCHAR(255)",
            "org_id": "VARCHAR(255)",
            "error_message": "TEXT",
            "s3_raw_data_key": "TEXT",
            "payload_preview": "TEXT",
            "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
        }

        await self.rds.create_table("founder_processing_errors", error_table_schema)

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and normalize string values."""
        if not value or not isinstance(value, str):
            return None

        cleaned = value.strip()
        if not cleaned:
            return None

        # Remove excessive whitespace
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _clean_url(self, value: Any) -> Optional[str]:
        """Clean and normalize URL values."""
        if not value or not isinstance(value, str):
            return None

        url = value.strip().lower()
        if not url:
            return None

        # Add protocol if missing
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        return url

    def _parse_date(self, date_str: Any) -> Optional[date]:
        """Parse date string to date object."""
        if not date_str:
            return None

        if isinstance(date_str, str):
            # Try different date formats
            for fmt in ["%Y-%m-%d", "%Y-%m", "%Y"]:
                try:
                    parsed = datetime.strptime(date_str, fmt)
                    return parsed.date()
                except ValueError:
                    continue

        return None

    def _extract_location_string(self, location_data: Any) -> Optional[str]:
        """Extract location string from various location formats."""
        if isinstance(location_data, str):
            return self._clean_string(location_data)
        elif isinstance(location_data, dict):
            parts = [
                location_data.get("locality"),
                location_data.get("region"),
                location_data.get("country"),
            ]
            # Filter out None values and clean strings
            location_parts = [self._clean_string(part) for part in parts if part]
            return ", ".join(location_parts) if location_parts else None  # type: ignore

        return None


class FounderEnrichmentService:
    """
    High-level service for founder enrichment processing.
    Orchestrates the entire enrichment workflow.
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.FounderEnrichmentService")
        self.rds_storage: Optional[RDSStorage] = None
        self.s3_storage: Optional[S3Storage] = None
        self.pdl_processor: Optional[PDLDataProcessor] = None

    async def initialize(self) -> None:
        """Initialize the enrichment service."""
        self.rds_storage = RDSStorage()
        self.s3_storage = S3Storage()

        await self.rds_storage.initialize()
        await self.s3_storage.initialize()

        self.pdl_processor = PDLDataProcessor(self.rds_storage, self.s3_storage)

        self.logger.info("Founder enrichment service initialized")

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.rds_storage:
            await self.rds_storage.cleanup()
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("Founder enrichment service cleaned up")

    async def process_enrichment_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a founder enrichment job.

        Args:
            job_data: Job configuration with enrichment data or PDL pull request

        Returns:
            Processing result with success status and metadata
        """
        try:
            # Extract job context
            job_context = self._extract_job_context(job_data)

            # Validate job context
            validation_error = self._validate_job_context(job_context)
            if validation_error:
                return self._create_error_response(job_context.job_id, validation_error)

            self.logger.info(
                "🚀 Starting founder enrichment",
                job_id=job_context.job_id,
                founder_name=job_context.founder_name,
                company_id=job_context.company_id,
                org_id=job_context.org_id,
            )
            # Store raw data to S3
            s3_key = await self._store_raw_data(job_data, job_context)

            # Run enrichment pipeline
            return await self._fetch_and_process_pdl_data(job_context, s3_key)

        except Exception as e:
            error_msg = f"Founder enrichment failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return self._create_error_response(
                job_data.get("job_id", "unknown"), error_msg, job_data.get("founder_id")
            )

    async def _fetch_and_process_pdl_data(
        self, job_context: JobContext, s3_key: str
    ) -> Dict[str, Any]:
        """Fetch PDL data and process it."""
        try:
            # Import here to avoid circular imports
            from app.pipelines.founder import FounderEnrichmentPipeline

            # Create pipeline instance
            pipeline = FounderEnrichmentPipeline()
            await pipeline.initialize()

            try:
                # Prepare input data for PDL enrichment
                input_data = {
                    "founder_id": job_context.founder_id,
                    "company_id": job_context.company_id,
                    "org_id": job_context.org_id,
                    "founder_name": job_context.founder_name,
                    "founder_linkedin": job_context.linkedin_url,
                }

                # Fetch PDL data
                self.logger.info(
                    "Fetching PDL data", founder_name=job_context.founder_name
                )
                pdl_result = await pipeline._process_data(input_data)

                if not pdl_result.success:
                    return self._create_error_response(
                        job_context.job_id,
                        f"PDL fetch failed: {pdl_result.error_message}",
                    )

                # Extract PDL data from result
                if not pdl_result.data:
                    self.logger.warning("No data returned from PDL API")
                    # Create a minimal founder record when enrichment fails
                    founder_uuid = self._generate_founder_uuid_from_context(job_context)
                    await self._create_minimal_founder_record(
                        job_context,
                        founder_uuid,
                        s3_key,
                        "No PDL data returned from API",
                    )
                    return {
                        "success": False,
                        "job_id": job_context.job_id,
                        "founder_id": job_context.founder_id,
                        "company_id": job_context.company_id,
                        "founder_uuid": founder_uuid,
                        "error": "No PDL data returned from API",
                        "s3_raw_data_key": s3_key,
                        "enrichment_status": "pdl_no_data",
                    }

                enrichment_data = pdl_result.data.get("pdl_data")
                if not enrichment_data:
                    self.logger.warning("No PDL data found in API response")
                    return self._create_error_response(
                        job_context.job_id, "No PDL data returned from API"
                    )

                # Convert PDL data to the format expected by PDLDataProcessor
                pdl_payload = {
                    "enrichment_data": enrichment_data.model_dump(),
                    "metadata": {
                        "founder_id": job_context.founder_id,
                        "company_id": job_context.company_id,
                        "org_id": job_context.org_id,
                        "founder_name": job_context.founder_name,
                        "confidence_score": pdl_result.metadata.get(
                            "confidence_score", 1.0
                        ),
                    },
                }

                # Process the PDL data
                if self.pdl_processor:
                    result = await self.pdl_processor.process_pdl_payload(
                        pdl_payload, s3_key
                    )

                    if result["success"]:
                        # Trigger signal generation after successful enrichment
                        await self._trigger_signal_generation(result["founder_uuid"])

                        return {
                            "success": True,
                            "job_id": job_context.job_id,
                            "founder_id": job_context.founder_id,
                            "company_id": job_context.company_id,
                            "founder_uuid": result["founder_uuid"],
                            "records_created": result["records_created"],
                            "s3_raw_data_key": s3_key,
                            "pdl_fetched": True,
                        }
                    else:
                        return {
                            "success": False,
                            "job_id": job_context.job_id,
                            "founder_id": job_context.founder_id,
                            "company_id": job_context.company_id,
                            "error": result["error"],
                            "s3_raw_data_key": s3_key,
                        }
                else:
                    return self._create_error_response(
                        job_context.job_id, "PDL processor not initialized"
                    )

            finally:
                await pipeline.cleanup()

        except Exception as e:
            error_msg = f"PDL fetch and process failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return self._create_error_response(job_context.job_id, error_msg)

    def _extract_job_context(self, job_data: Dict[str, Any]) -> JobContext:
        """Extract job context from job data."""
        # Handle nested form_data structure
        form_data = job_data.get("form_data", {})

        # Extract founder info from form_data or direct fields
        founder_name = (
            form_data.get("founder_name")
            or job_data.get("founder_name")
            or job_data.get("metadata", {}).get("founder_name")
        )

        linkedin_url = (
            form_data.get("founder_linkedin")
            or job_data.get("founder_linkedin")
            or job_data.get("metadata", {}).get("founder_linkedin")
        )

        # Generate founder_id if not provided
        founder_id = job_data.get("founder_id")
        if not founder_id:
            # Generate a deterministic founder_id based on name and LinkedIn URL
            founder_name_clean = (founder_name or "").strip().lower()
            linkedin_url_clean = (linkedin_url or "").strip().lower()

            # Create a simple hash-based ID
            import hashlib

            id_string = f"{founder_name_clean}|{linkedin_url_clean}"
            founder_id = hashlib.md5(id_string.encode()).hexdigest()[:12]

        return JobContext(
            job_id=job_data.get("job_id", "unknown"),
            founder_id=founder_id,
            company_id=job_data.get(
                "company_id", job_data.get("metadata", {}).get("company_id")
            ),
            org_id=job_data.get("org_id", job_data.get("metadata", {}).get("org_id")),
            founder_name=founder_name,
            linkedin_url=linkedin_url or "",
        )

    def _validate_job_context(self, job_context: JobContext) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.founder_name:
            return "Missing founder_name"
        if not job_context.linkedin_url:
            return "Missing founder_linkedin"
        return None

    def _create_error_response(
        self, job_id: str, error: str, founder_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "success": False,
            "job_id": job_id,
            "founder_id": founder_id,
            "error": error,
        }

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: JobContext
    ) -> str:
        """Store raw job data to S3 for audit trail."""
        import json

        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"founders/{job_context.org_id}/{job_context.founder_id}/raw_data_{timestamp}.json"

        # Store data
        if self.s3_storage:
            await self.s3_storage.put_object(s3_key, json.dumps(job_data, default=str))

        return s3_key

    def _generate_founder_uuid_from_context(self, job_context: JobContext) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL from job context."""
        founder_name = (job_context.founder_name or "").strip().lower()
        linkedin_url = (job_context.linkedin_url or "").strip().lower()

        # Create deterministic string for UUID generation
        uuid_string = f"{founder_name}|{linkedin_url}"

        # Generate UUIDv5 using DNS namespace
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    async def _create_minimal_founder_record(
        self,
        job_context: JobContext,
        founder_uuid: str,
        s3_key: str,
        error_message: str,
    ) -> None:
        """Create a minimal founder record when enrichment fails."""
        try:
            now = datetime.now(timezone.utc)

            founder_record = {
                "id": founder_uuid,
                "founder_id": job_context.founder_id,
                "full_name": job_context.founder_name,
                "first_name": None,
                "last_name": None,
                "current_job_title": None,
                "current_job_company": None,
                "linkedin_url": job_context.linkedin_url,
                "github_url": None,
                "location_country": None,
                "org_id": job_context.org_id,
                "company_id": job_context.company_id,
                "source": "pdl_no_data",
                "confidence_score": 0.0,
                "enrichment_date": None,
                "s3_raw_data_key": s3_key,
                "created_at": now,
                "updated_at": now,
            }

            # Store in database
            if self.rds_storage:
                await self.rds_storage.upsert(
                    table_name="founders",
                    data=founder_record,
                    key_fields=["id", "company_id"],
                )

            self.logger.info(
                f"Created minimal founder record for failed enrichment: {founder_uuid}",
                founder_name=job_context.founder_name,
                error=error_message,
            )

        except Exception as e:
            self.logger.error(
                f"Failed to create minimal founder record: {str(e)}", exc_info=True
            )

    async def _trigger_signal_generation(self, founder_uuid: str) -> None:
        """
        Trigger signal generation for a founder after successful enrichment.

        Args:
            founder_uuid: The UUID of the founder to generate signals for
        """
        try:
            self.logger.info(f"Triggering signal generation for founder {founder_uuid}")

            # Import here to avoid circular imports
            from app.tasks.signal_generation import generate_founder_signals

            # Trigger signal generation asynchronously
            # Note: This runs in the same process for now, but could be queued
            signal_result = await generate_founder_signals(founder_uuid)

            if signal_result["success"]:
                self.logger.info(
                    f"Signal generation completed for founder {founder_uuid}",
                    score=signal_result.get("score"),
                    tags=signal_result.get("tags"),
                )
            else:
                self.logger.warning(
                    f"Signal generation failed for founder {founder_uuid}",
                    error=signal_result.get("error"),
                )

        except Exception as e:
            self.logger.error(
                f"Error triggering signal generation for founder {founder_uuid}: {e}",
                exc_info=True,
            )
            # Don't fail the enrichment job if signal generation fails
            # Signal generation is a nice-to-have, not critical for enrichment


async def enrich_founder_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Modern founder enrichment using PDL data with normalized database storage.

    This function processes founder enrichment requests by:
    1. Validating input data and generating founder_id if needed
    2. Storing raw data to S3 for audit trails
    3. Fetching PDL data from API
    4. Processing and normalizing data into structured database tables

    Args:
        job_data: Enrichment job configuration containing:
            - company_id: Company identifier
            - org_id: Organization identifier
            - founder_name: Founder's name
            - founder_linkedin: LinkedIn URL
            - founder_id: (optional) Pre-generated founder ID
            - job_id: (optional) Unique job identifier

    Returns:
        Processing result with success status and metadata
    """
    service = FounderEnrichmentService()

    try:
        await service.initialize()
        return await service.process_enrichment_job(job_data)
    finally:
        await service.cleanup()


def enrich_founder_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async founder enrichment task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(enrich_founder_data(job_data))
