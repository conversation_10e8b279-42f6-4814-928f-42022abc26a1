"""
Configuration settings for TractionX Data Pipeline Service.
"""

from typing import Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True
    )

    # Service Settings
    SERVICE_NAME: str = "tractionx-datapipelines"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"

    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8001
    API_PREFIX: str = "/api/v1"

    # Redis (RQ Queue)
    REDIS_HOST: Optional[str]
    REDIS_PORT: Optional[int]
    REDIS_USERNAME: Optional[str]
    REDIS_DB: Optional[str]
    REDIS_ENDPOINT: Optional[str] = None
    REDIS_PASSWORD: Optional[str] = None
    REDIS_KEY_PREFIX: str = "tx_datapipelines"

    # Database (PostgreSQL)
    DB_HOST: str
    DB_PORT: int
    DB_NAME: str
    DB_USER: str
    DB_PASSWORD: str
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20

    # S3 Storage
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    S3_BUCKET: str
    S3_PREFIX: str

    # Qdrant Vector Database
    QDRANT_URL: str
    QDRANT_API_KEY: Optional[str] = None
    QDRANT_VECTOR_SIZE: int = 1536

    # External API Keys
    CLAY_API_KEY: Optional[str] = None
    CLAY_BASE_URL: str = "https://api.clay.com/v1"
    APOLLO_API_KEY: Optional[str] = None
    APOLLO_BASE_URL: str = "https://api.apollo.io"
    PDL_API_KEY: Optional[str] = None
    PDL_BASE_URL: str
    NEWSAPI_KEY: Optional[str]
    GNEWS_KEY: str
    SERPAPI_KEY: str
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-3-small"
    OPENAI_MODEL: str

    # Worker Settings
    WORKER_CONCURRENCY: int = 4
    WORKER_POLL_INTERVAL: int = 5
    WORKER_TIMEOUT: int = 1800  # 30 minutes
    MAX_RETRIES: int = 3
    RETRY_BACKOFF_FACTOR: int = 2

    # Pipeline Settings
    PIPELINE_TIMEOUT: int = 3600  # 1 hour
    ENABLE_COMPANY_ENRICHMENT: bool = True
    ENABLE_FOUNDER_ENRICHMENT: bool = True
    ENABLE_NEWS_AGGREGATION: bool = True
    ENABLE_EMBEDDING_GENERATION: bool = True

    # Webhook Settings
    WEBHOOK_SECRET: Optional[str] = None
    CLAY_WEBHOOK_SECRET: Optional[str] = None

    # Backend Integration
    BACKEND_API_URL: str = "http://localhost:8000/api/v1"
    BACKEND_API_KEY: Optional[str] = None

    # Monitoring
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_PORT: int = 9090
    ENABLE_METRICS: bool = True

    # Rate Limiting (requests per minute)
    CLAY_RATE_LIMIT: int = 100
    APOLLO_RATE_LIMIT: int = 1000
    PDL_RATE_LIMIT: int = 1000
    BING_RATE_LIMIT: int = 1000
    OPENAI_RATE_LIMIT: int = 3000

    # Data Retention (days)
    RAW_DATA_RETENTION_DAYS: int = 90
    PROCESSED_DATA_RETENTION_DAYS: int = 365
    LOG_RETENTION_DAYS: int = 30

    WATCHDOG_ENABLED: bool = True
    RELOAD_DIRS: str

    @field_validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()

    @field_validator("ENVIRONMENT")
    def validate_environment(cls, v):
        valid_envs = ["development", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"ENVIRONMENT must be one of {valid_envs}")
        return v.lower()

    @property
    def redis_connection_string(self) -> str:
        """Get Redis connection string."""
        auth = f"{self.REDIS_USERNAME}:{self.REDIS_PASSWORD}@"
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}"

    @property
    def database_connection_string(self) -> str:
        return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @property
    def is_production(self) -> bool:
        """Check if running in production."""
        return self.ENVIRONMENT == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development."""
        return self.ENVIRONMENT == "development"


# Global settings instance
settings = Settings()  # type: ignore
