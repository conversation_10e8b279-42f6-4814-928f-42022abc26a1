"""
Apollo Company Data Transformer for TractionX Data Pipeline Service.

This module transforms Apollo API company enrichment payloads into normalized
structured data for storage in relational database tables.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from app.configs import get_logger
from app.models.company import (
    ApolloCompanyData,
    CompanyDepartmentCount,
    CompanyKeyword,
    CompanyTechnology,
)

logger = get_logger(__name__)


def transform_apollo_company_payload(
    payload: Dict[str, Any], org_id: str
) -> Dict[str, Any]:
    """
    Transform Apollo company enrichment payload into normalized data structure.
    
    Args:
        payload: Raw Apollo API response payload
        org_id: Organization identifier
        
    Returns:
        Dictionary containing:
        - company: Flat company data dict
        - keywords: List of keyword strings
        - technologies: List of technology dicts with category
        - departments: List of department count dicts
    """
    try:
        logger.info("Transforming Apollo company payload", org_id=org_id)
        
        # Extract company data from Apollo response
        company_data = _extract_company_data(payload, org_id)
        
        # Extract keywords
        keywords = _extract_keywords(payload)
        
        # Extract technologies with categories
        technologies = _extract_technologies(payload)
        
        # Extract department counts
        departments = _extract_department_counts(payload)
        
        result = {
            "company": company_data,
            "keywords": keywords,
            "technologies": technologies,
            "departments": departments,
        }
        
        logger.info(
            "Apollo transformation completed",
            org_id=org_id,
            keywords_count=len(keywords),
            technologies_count=len(technologies),
            departments_count=len(departments),
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Apollo transformation failed: {e}", exc_info=True)
        raise


def _extract_company_data(payload: Dict[str, Any], org_id: str) -> Dict[str, Any]:
    """Extract and clean core company data from Apollo payload."""
    
    # Create Apollo data model first
    apollo_data = ApolloCompanyData(
        apollo_id=_clean_string(payload.get("id")),
        name=_clean_string(payload.get("name")),
        domain=_clean_string(payload.get("domain")),
        website=_clean_url(payload.get("website_url")),
        description=_clean_string(payload.get("description")),
        industry=_clean_string(payload.get("industry")),
        sub_industry=_clean_string(payload.get("sub_industry")),
        employee_count=_clean_integer(payload.get("estimated_num_employees")),
        employee_count_range=_clean_string(payload.get("employee_count_range")),
        founded_year=_clean_integer(payload.get("founded_year")),
        headquarters=_clean_string(payload.get("headquarters")),
        country=_clean_string(payload.get("country")),
        city=_clean_string(payload.get("city")),
        state=_clean_string(payload.get("state")),
        funding_total=_clean_float(payload.get("total_funding")),
        funding_rounds=_clean_integer(payload.get("funding_rounds")),
        last_funding_date=_parse_date(payload.get("last_funding_date")),
        last_funding_amount=_clean_float(payload.get("last_funding_amount")),
        valuation=_clean_float(payload.get("valuation")),
        revenue=_clean_float(payload.get("annual_revenue")),
        technologies=_extract_technology_names(payload),
        tech_stack=payload.get("tech_stack", {}),
        linkedin_url=_clean_url(payload.get("linkedin_url")),
        twitter_url=_clean_url(payload.get("twitter_url")),
        facebook_url=_clean_url(payload.get("facebook_url")),
        email=_clean_string(payload.get("email")),
        phone=_clean_string(payload.get("phone")),
        apollo_metadata={
            "api_response_timestamp": time.time(),
            "raw_payload_keys": list(payload.keys()),
            "enrichment_confidence": payload.get("confidence_score", 1.0),
        },
    )
    
    # Convert to flat dict for database storage, excluding fields that go to separate tables
    company_dict = apollo_data.model_dump()

    # Remove fields that should be stored in separate tables
    company_dict.pop('keywords', None)
    company_dict.pop('departmental_head_count', None)
    company_dict.pop('technologies', None)  # This will be in company_technologies table

    return company_dict


def _extract_keywords(payload: Dict[str, Any]) -> List[str]:
    """Extract and clean keywords from Apollo payload."""
    keywords = []
    
    # Extract from various keyword fields
    keyword_sources = [
        payload.get("keywords", []),
        payload.get("tags", []),
        payload.get("categories", []),
    ]
    
    for source in keyword_sources:
        if isinstance(source, list):
            for keyword in source:
                cleaned = _clean_string(keyword)
                if cleaned and cleaned not in keywords:
                    keywords.append(cleaned)
        elif isinstance(source, str):
            cleaned = _clean_string(source)
            if cleaned and cleaned not in keywords:
                keywords.append(cleaned)
    
    # Also extract keywords from description if available
    description = payload.get("description", "")
    if description:
        # Simple keyword extraction from description
        # This could be enhanced with NLP in the future
        pass
    
    return sorted(keywords)


def _extract_technologies(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract technologies with categories from Apollo payload."""
    technologies = []
    
    # Extract from tech_stack
    tech_stack = payload.get("tech_stack", {})
    for category, tech_list in tech_stack.items():
        if isinstance(tech_list, list):
            for tech in tech_list:
                tech_name = _clean_string(tech)
                if tech_name:
                    technologies.append({
                        "technology": tech_name,
                        "category": _clean_string(category),
                    })
    
    # Extract from technologies array
    tech_array = payload.get("technologies", [])
    if isinstance(tech_array, list):
        for tech in tech_array:
            if isinstance(tech, dict):
                tech_name = _clean_string(tech.get("name"))
                category = _clean_string(tech.get("category"))
            else:
                tech_name = _clean_string(tech)
                category = None
                
            if tech_name:
                # Avoid duplicates
                existing = next(
                    (t for t in technologies if t["technology"] == tech_name), None
                )
                if not existing:
                    technologies.append({
                        "technology": tech_name,
                        "category": category,
                    })
    
    return technologies


def _extract_department_counts(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract department head counts from Apollo payload."""
    departments = []
    
    dept_counts = payload.get("departmental_head_count", {})
    if isinstance(dept_counts, dict):
        for dept_name, count in dept_counts.items():
            cleaned_dept = _clean_string(dept_name)
            cleaned_count = _clean_integer(count)
            
            if cleaned_dept and cleaned_count is not None and cleaned_count > 0:
                departments.append({
                    "department": cleaned_dept,
                    "head_count": cleaned_count,
                })
    
    return departments


def _extract_technology_names(payload: Dict[str, Any]) -> List[str]:
    """Extract just technology names for the Apollo model."""
    tech_names = []
    
    # From tech_stack
    tech_stack = payload.get("tech_stack", {})
    for tech_list in tech_stack.values():
        if isinstance(tech_list, list):
            for tech in tech_list:
                tech_name = _clean_string(tech)
                if tech_name and tech_name not in tech_names:
                    tech_names.append(tech_name)
    
    # From technologies array
    tech_array = payload.get("technologies", [])
    if isinstance(tech_array, list):
        for tech in tech_array:
            if isinstance(tech, dict):
                tech_name = _clean_string(tech.get("name"))
            else:
                tech_name = _clean_string(tech)
                
            if tech_name and tech_name not in tech_names:
                tech_names.append(tech_name)
    
    return sorted(tech_names)



def _clean_string(value: Any) -> Optional[str]:
    """Clean and normalize string values."""
    if not value or not isinstance(value, str):
        return None
    
    cleaned = value.strip()
    if not cleaned:
        return None
    
    # Remove excessive whitespace
    cleaned = " ".join(cleaned.split())
    
    return cleaned


def _clean_url(value: Any) -> Optional[str]:
    """Clean and normalize URL values."""
    if not value or not isinstance(value, str):
        return None
    
    url = value.strip()
    if not url:
        return None
    
    # Add protocol if missing
    if not url.startswith(("http://", "https://")):
        url = f"https://{url}"
    
    return url


def _clean_integer(value: Any) -> Optional[int]:
    """Clean and convert to integer."""
    if value is None:
        return None
    
    if isinstance(value, int):
        return value
    
    if isinstance(value, str):
        try:
            return int(value.replace(",", ""))
        except ValueError:
            return None
    
    if isinstance(value, float):
        return int(value)
    
    return None


def _clean_float(value: Any) -> Optional[float]:
    """Clean and convert to float."""
    if value is None:
        return None
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        try:
            return float(value.replace(",", ""))
        except ValueError:
            return None
    
    return None


def _parse_date(date_str: Any) -> Optional[datetime]:
    """Parse date string to datetime object."""
    if not date_str:
        return None
    
    if isinstance(date_str, str):
        # Try different date formats
        for fmt in ["%Y-%m-%d", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%SZ"]:
            try:
                return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue
    
    return None
