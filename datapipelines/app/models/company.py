"""
Company data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic import Field, HttpUrl

from app.models.base import EnrichmentSource, TractionXModel


class CompanyData(TractionXModel):
    """Core company data structure."""

    # Core identifiers
    company_id: str = Field(..., description="Unique company identifier")
    name: str = Field(..., description="Company name")

    # Basic information
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[HttpUrl] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    industry: Optional[str] = Field(None, description="Industry/sector")
    stage: Optional[str] = Field(
        None, description="Company stage (seed, series A, etc.)"
    )
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Number of employees")

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")

    # Financial
    funding_total: Optional[float] = Field(None, description="Total funding raised")
    valuation: Optional[float] = Field(None, description="Company valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Social/Contact
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[HttpUrl] = Field(None, description="Twitter URL")
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Contact phone")

    # Metadata
    source: EnrichmentSource = Field(..., description="Data source")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence"
    )

    # Additional data
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional company data"
    )


class ApolloCompanyData(TractionXModel):
    """Company data from Apollo enrichment service."""

    # Apollo-specific fields
    apollo_id: Optional[str] = Field(None, description="Apollo company ID")

    # Basic info
    name: Optional[str] = Field(None, description="Company name")
    domain: Optional[str] = Field(None, description="Company domain")
    website: Optional[str] = Field(None, description="Company website")
    description: Optional[str] = Field(None, description="Company description")

    # Business details
    industry: Optional[str] = Field(None, description="Industry")
    sub_industry: Optional[str] = Field(None, description="Sub-industry")
    employee_count: Optional[int] = Field(None, description="Employee count")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )
    founded_year: Optional[int] = Field(None, description="Year founded")

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")

    # Financial
    funding_total: Optional[float] = Field(None, description="Total funding")
    funding_rounds: Optional[int] = Field(None, description="Number of funding rounds")
    last_funding_date: Optional[datetime] = Field(None, description="Last funding date")
    last_funding_amount: Optional[float] = Field(
        None, description="Last funding amount"
    )
    valuation: Optional[float] = Field(None, description="Valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Technology
    technologies: List[str] = Field(
        default_factory=list, description="Technologies used"
    )
    tech_stack: Dict[str, Any] = Field(
        default_factory=dict, description="Technology stack details"
    )

    # Keywords
    keywords: List[str] = Field(
        default_factory=list, description="Company keywords"
    )

    # Department information
    departmental_head_count: Dict[str, int] = Field(
        default_factory=dict, description="Department head counts"
    )

    # Social presence
    linkedin_url: Optional[str] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[str] = Field(None, description="Twitter URL")
    facebook_url: Optional[str] = Field(None, description="Facebook URL")

    # Contact info
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")

    # Additional Apollo data
    apollo_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Apollo-specific metadata"
    )
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc)
    )

    def to_company_data(self, company_id: str) -> CompanyData:
        """Convert Apollo data to standard CompanyData format."""
        return CompanyData(
            company_id=company_id,
            name=self.name or "",
            description=self.description,
            website=self.website,  # type: ignore
            domain=self.domain,
            industry=self.industry,
            employee_count=self.employee_count,
            headquarters=self.headquarters,
            country=self.country,
            city=self.city,
            funding_total=self.funding_total,
            valuation=self.valuation,
            linkedin_url=self.linkedin_url,  # type: ignore
            twitter_url=self.twitter_url,  # type: ignore
            email=self.email,
            phone=self.phone,
            source=EnrichmentSource.APOLLO,
            founded_year=self.founded_year,
            revenue=self.revenue,
            additional_data={
                "apollo_id": self.apollo_id,
                "sub_industry": self.sub_industry,
                "employee_count_range": self.employee_count_range,
                "state": self.state,
                "funding_rounds": self.funding_rounds,
                "last_funding_date": self.last_funding_date.isoformat()
                if self.last_funding_date
                else None,
                "last_funding_amount": self.last_funding_amount,
                "technologies": self.technologies,
                "tech_stack": self.tech_stack,
                "keywords": self.keywords,
                "departmental_head_count": self.departmental_head_count,
                "facebook_url": self.facebook_url,
                "apollo_metadata": self.apollo_metadata,
                "enrichment_date": self.enrichment_date.isoformat(),
            },
            stage=None,
            confidence_score=None,
        )


class CompanyEnrichmentData(TractionXModel):
    """Enriched company data from multiple sources."""

    # Core company info
    company_id: str = Field(..., description="Unique company identifier")
    org_id: str = Field(..., description="Organization ID")

    # Form submission data (highest priority)
    form_data: Optional[CompanyData] = Field(
        None, description="Data from form submission"
    )

    # Enrichment data
    apollo_data: Optional[ApolloCompanyData] = Field(
        None, description="Apollo enrichment data"
    )

    # Processing metadata
    enrichment_status: Dict[str, str] = Field(
        default_factory=dict, description="Status of each enrichment"
    )
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Quality metrics
    completeness_score: Optional[float] = Field(
        None, description="Data completeness score"
    )
    confidence_score: Optional[float] = Field(
        None, description="Overall confidence score"
    )

    def get_canonical_data(self) -> Optional[CompanyData]:
        """Get canonical company data using merge rules (form > apollo)."""
        # Start with form data if available
        if self.form_data:
            canonical = self.form_data.model_copy()
        else:
            canonical = None

        # Merge Apollo data for missing fields
        if self.apollo_data:
            apollo_company = self.apollo_data.to_company_data(self.company_id)

            # Fill missing fields with Apollo data
            for field_name, field_value in apollo_company.model_dump().items():
                if field_name in ["company_id", "source", "last_updated"]:
                    continue

                current_value = getattr(canonical, field_name, None)
                if current_value is None and field_value is not None:
                    setattr(canonical, field_name, field_value)

        return canonical


class CompanyKeyword(TractionXModel):
    """Company keyword model for normalized storage."""

    id: str = Field(..., description="Unique keyword record ID")
    company_id: str = Field(..., description="Company identifier")
    keyword: str = Field(..., description="Keyword text")


class CompanyTechnology(TractionXModel):
    """Company technology model for normalized storage."""

    id: str = Field(..., description="Unique technology record ID")
    company_id: str = Field(..., description="Company identifier")
    technology: str = Field(..., description="Technology name")
    category: Optional[str] = Field(None, description="Technology category")


class CompanyDepartmentCount(TractionXModel):
    """Company department count model for normalized storage."""

    id: str = Field(..., description="Unique department record ID")
    company_id: str = Field(..., description="Company identifier")
    department: str = Field(..., description="Department name")
    head_count: int = Field(..., description="Number of employees in department")
