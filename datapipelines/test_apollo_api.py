#!/usr/bin/env python3
"""
Apollo API Test Script for TractionX Company Enrichment Pipeline.

This script tests the Apollo API integration to ensure the pipeline
can successfully fetch company data from Apollo's enrichment endpoint.

Usage:
    export APOLLO_API_KEY=your_apollo_api_key
    python test_apollo_api.py
"""

import asyncio
import json
import os
import sys
from typing import Dict, Any

import httpx


async def test_apollo_api(domain: str, api_key: str) -> Dict[str, Any]:
    """
    Test Apollo API enrichment endpoint.
    
    Args:
        domain: Company domain to enrich
        api_key: Apollo API key
        
    Returns:
        API response or error information
    """
    print(f"🔍 Testing Apollo API for domain: {domain}")
    
    # Apollo API configuration
    base_url = "https://api.apollo.io"
    endpoint = "/api/v1/organizations/enrich"
    
    headers = {
        "x-api-key": api_key,
        "Content-Type": "application/json",
        "accept": "application/json",
        "Cache-Control": "no-cache"
    }
    
    params = {
        "domain": domain
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"📡 Making request to: {base_url}{endpoint}")
            print(f"📋 Parameters: {params}")
            
            response = await client.get(
                f"{base_url}{endpoint}",
                headers=headers,
                params=params
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Apollo API request successful!")
                
                # Extract key information
                if "organization" in data:
                    org = data["organization"]
                    print(f"🏢 Company: {org.get('name', 'N/A')}")
                    print(f"🌐 Domain: {org.get('primary_domain', 'N/A')}")
                    print(f"👥 Employees: {org.get('estimated_num_employees', 'N/A')}")
                    print(f"🏭 Industry: {org.get('industry', 'N/A')}")
                    print(f"📍 Location: {org.get('city', 'N/A')}, {org.get('country', 'N/A')}")
                    
                    # Check for enrichment data
                    tech_count = len(org.get("technologies", []))
                    dept_count = len(org.get("departmental_head_count", {}))
                    keywords_count = len(org.get("keywords", []))
                    
                    print(f"🔧 Technologies: {tech_count}")
                    print(f"🏢 Departments: {dept_count}")
                    print(f"🏷️  Keywords: {keywords_count}")
                    
                return {
                    "success": True,
                    "status_code": response.status_code,
                    "data": data,
                    "message": "Apollo API request successful"
                }
                
            elif response.status_code == 401:
                print("❌ Authentication failed - check your Apollo API key")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": "Authentication failed",
                    "message": "Invalid or missing Apollo API key"
                }
                
            elif response.status_code == 404:
                print(f"❌ Company not found for domain: {domain}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": "Company not found",
                    "message": f"No company data found for domain: {domain}"
                }
                
            elif response.status_code == 429:
                print("❌ Rate limit exceeded")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": "Rate limit exceeded",
                    "message": "Apollo API rate limit exceeded"
                }
                
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                print(f"Response: {response.text}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": "Unexpected response",
                    "message": response.text
                }
                
    except httpx.TimeoutException:
        print("❌ Request timeout")
        return {
            "success": False,
            "error": "Timeout",
            "message": "Apollo API request timed out"
        }
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return {
            "success": False,
            "error": "Request failed",
            "message": str(e)
        }


async def main():
    """Main test function."""
    print("🧪 Apollo API Integration Test")
    print("=" * 50)
    
    # Check for API key
    api_key = os.getenv("APOLLO_API_KEY")
    if not api_key:
        print("❌ APOLLO_API_KEY environment variable not set")
        print("Usage: export APOLLO_API_KEY=your_apollo_api_key && python test_apollo_api.py")
        sys.exit(1)
    
    # Test domains
    test_domains = [
        "kubegrade.com",  # Example from the curl command
        "apollo.io",      # Apollo's own domain
        "openai.com",     # Well-known tech company
    ]
    
    results = []
    
    for domain in test_domains:
        print(f"\n🔍 Testing domain: {domain}")
        print("-" * 30)
        
        result = await test_apollo_api(domain, api_key)
        results.append((domain, result))
        
        # Add delay between requests to respect rate limits
        await asyncio.sleep(1)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    successful = 0
    for domain, result in results:
        status = "✅ SUCCESS" if result["success"] else "❌ FAILED"
        print(f"{status} - {domain}")
        if result["success"]:
            successful += 1
        else:
            print(f"   Error: {result.get('message', 'Unknown error')}")
    
    print(f"\n🎯 Overall: {successful}/{len(results)} tests passed")
    
    if successful > 0:
        print("🎉 Apollo API integration is working!")
        print("\n💡 Next steps:")
        print("   1. Add your Apollo API key to datapipelines/.env")
        print("   2. Test the full company enrichment pipeline")
        print("   3. Verify database table creation and data insertion")
    else:
        print("⚠️  Apollo API integration needs attention")
        print("\n🔧 Troubleshooting:")
        print("   1. Verify your Apollo API key is correct")
        print("   2. Check your Apollo account status and rate limits")
        print("   3. Ensure network connectivity to api.apollo.io")


if __name__ == "__main__":
    asyncio.run(main())
