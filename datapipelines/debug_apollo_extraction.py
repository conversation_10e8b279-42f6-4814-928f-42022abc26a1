#!/usr/bin/env python3
"""
Debug Apollo Data Extraction - Simple Test Without Dependencies.

This script manually tests the extraction logic using the actual Apollo response
to identify why the extraction is returning 0 results.
"""

# Simulate the Apollo response structure from your logs
APOLLO_RESPONSE = {
    "organization": {
        "id": "678ca83f2874280001fefd7c",
        "name": "Kubegrade",
        "keywords": [
            "kubernetes & devops", "kubernetes", "ai agents", "infrastructure", "ai", "devops", "cloud",
            "software development", "cluster management", "security solutions", "upgrade automation"
        ],
        "current_technologies": [
            {"uid": "gmail", "name": "Gmail", "category": "Email Providers"},
            {"uid": "google_apps", "name": "Google Apps", "category": "Other"},
            {"uid": "nginx", "name": "Nginx", "category": "Load Balancers"}
        ],
        "technology_names": ["Gmail", "Google Apps", "Gravity Forms", "Mobile Friendly", "Nginx"],
        "departmental_head_count": {
            "entrepreneurship": 1,
            "arts_and_design": 1,
            "engineering": 3,
            "operations": 1,
            "marketing": 1,
            "accounting": 0,
            "sales": 0
        },
        "industries": ["information technology & services"]
    }
}

def _clean_string(value):
    """Simple string cleaning function."""
    if not value or not isinstance(value, str):
        return None
    cleaned = value.strip()
    return cleaned if cleaned else None

def _clean_integer(value):
    """Simple integer cleaning function."""
    if value is None:
        return None
    if isinstance(value, int):
        return value
    if isinstance(value, str):
        try:
            return int(value.replace(",", ""))
        except ValueError:
            return None
    if isinstance(value, float):
        return int(value)
    return None

def test_keyword_extraction():
    """Test keyword extraction logic."""
    print("🏷️  Testing Keyword Extraction")
    print("-" * 40)
    
    # Get organization data
    org_data = APOLLO_RESPONSE.get("organization", {})
    print(f"Organization data keys: {list(org_data.keys())}")
    
    keywords = []
    
    # Extract keywords
    apollo_keywords = org_data.get("keywords", [])
    print(f"Found {len(apollo_keywords)} keywords in Apollo response")
    print(f"Sample keywords: {apollo_keywords[:3]}")
    
    if isinstance(apollo_keywords, list):
        for keyword in apollo_keywords:
            cleaned = _clean_string(keyword)
            if cleaned and cleaned not in keywords:
                keywords.append(cleaned)
    
    # Extract industries
    industries = org_data.get("industries", [])
    print(f"Found {len(industries)} industries")
    
    if isinstance(industries, list):
        for industry in industries:
            cleaned = _clean_string(industry)
            if cleaned and cleaned not in keywords:
                keywords.append(cleaned)
    
    print(f"Final result: {len(keywords)} keywords")
    print(f"Sample final keywords: {keywords[:5]}")
    return keywords

def test_technology_extraction():
    """Test technology extraction logic."""
    print("\n🔧 Testing Technology Extraction")
    print("-" * 40)
    
    # Get organization data
    org_data = APOLLO_RESPONSE.get("organization", {})
    
    technologies = []
    
    # Extract current_technologies
    current_technologies = org_data.get("current_technologies", [])
    print(f"Found {len(current_technologies)} current_technologies")
    
    if isinstance(current_technologies, list):
        for tech in current_technologies:
            if isinstance(tech, dict):
                tech_name = _clean_string(tech.get("name"))
                category = _clean_string(tech.get("category"))
                
                if tech_name:
                    technologies.append({
                        "technology": tech_name,
                        "category": category,
                    })
    
    # Extract technology_names
    tech_names = org_data.get("technology_names", [])
    print(f"Found {len(tech_names)} technology_names")
    
    if isinstance(tech_names, list):
        for tech_name in tech_names:
            cleaned_name = _clean_string(tech_name)
            if cleaned_name:
                # Check for duplicates
                existing = next(
                    (t for t in technologies if t["technology"] == cleaned_name), None
                )
                if not existing:
                    technologies.append({
                        "technology": cleaned_name,
                        "category": None,
                    })
    
    print(f"Final result: {len(technologies)} technologies")
    for tech in technologies[:3]:
        print(f"  • {tech['technology']} ({tech['category']})")
    
    return technologies

def test_department_extraction():
    """Test department extraction logic."""
    print("\n🏢 Testing Department Extraction")
    print("-" * 40)
    
    # Get organization data
    org_data = APOLLO_RESPONSE.get("organization", {})
    
    departments = []
    
    dept_counts = org_data.get("departmental_head_count", {})
    print(f"Found departmental_head_count with {len(dept_counts)} departments")
    
    if isinstance(dept_counts, dict):
        for dept_name, count in dept_counts.items():
            cleaned_dept = _clean_string(dept_name)
            cleaned_count = _clean_integer(count)
            
            if cleaned_dept and cleaned_count is not None and cleaned_count >= 0:
                # Convert snake_case to readable format
                readable_dept = cleaned_dept.replace("_", " ").title()
                departments.append({
                    "department": readable_dept,
                    "head_count": cleaned_count,
                })
    
    print(f"Final result: {len(departments)} departments")
    for dept in departments[:3]:
        print(f"  • {dept['department']}: {dept['head_count']}")
    
    return departments

def main():
    """Run all extraction tests."""
    print("🧪 Apollo Data Extraction Debug Test")
    print("=" * 60)
    
    # Test each extraction function
    keywords = test_keyword_extraction()
    technologies = test_technology_extraction()
    departments = test_department_extraction()
    
    print(f"\n" + "=" * 60)
    print("📊 Summary:")
    print(f"   Keywords: {len(keywords)}")
    print(f"   Technologies: {len(technologies)}")
    print(f"   Departments: {len(departments)}")
    
    if len(keywords) > 0 and len(technologies) > 0 and len(departments) > 0:
        print("\n✅ All extraction functions working correctly!")
        print("   The issue might be in how the data is being passed to the functions.")
    else:
        print("\n❌ Some extraction functions are not working.")
        print("   Check the logic and data structure.")

if __name__ == "__main__":
    main()
