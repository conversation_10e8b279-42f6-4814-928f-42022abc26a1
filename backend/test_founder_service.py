#!/usr/bin/env python3
"""
Simple test script for the founder service.
This script tests the founder service functionality without requiring a full FastAPI setup.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.services.founder.service import FounderService


async def test_founder_service():
    """Test the founder service initialization and basic functionality."""
    print("Testing Founder Service...")
    
    try:
        # Initialize the service
        founder_service = FounderService()
        print("✓ Founder service created successfully")
        
        # Test camelCase conversion
        test_data = {
            "founder": {
                "full_name": "<PERSON>",
                "linkedin_url": "https://linkedin.com/in/johndoe",
                "location_country": "USA"
            },
            "experiences": [
                {
                    "company_name": "Tech Corp",
                    "start_date": "2020-01-01",
                    "end_date": "2023-01-01"
                }
            ],
            "skills": [
                {"skill": "Python"},
                {"skill": "Leadership"}
            ]
        }
        
        camel_case_data = founder_service._convert_to_camel_case(test_data)
        print("✓ CamelCase conversion works")
        
        # Verify the conversion
        assert "fullName" in camel_case_data["founder"]
        assert "linkedinUrl" in camel_case_data["founder"]
        assert "locationCountry" in camel_case_data["founder"]
        assert "companyName" in camel_case_data["experiences"][0]
        assert "startDate" in camel_case_data["experiences"][0]
        assert "endDate" in camel_case_data["experiences"][0]
        print("✓ CamelCase conversion verified")
        
        print("\n🎉 All tests passed!")
        print("\nNote: Database connection tests require a running PostgreSQL instance.")
        print("The service is ready to be used with proper database configuration.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(test_founder_service())
    sys.exit(0 if success else 1)
