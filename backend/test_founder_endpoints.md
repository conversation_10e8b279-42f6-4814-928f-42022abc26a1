# Founder Analysis API Testing Guide

This document provides examples for testing the new founder analysis endpoints.

## Prerequisites

1. Backend service running on port 8000
2. Datapipelines service running on port 8001 (for enrichment trigger)
3. Valid authentication token
4. Organization ID in headers

## Endpoints

### 1. Get Deal Founders

**Endpoint:** `GET /api/v1/deals/{deal_id}/founders`

**Description:** Returns all enriched founder data tied to a deal

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/deals/{deal_id}/founders" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "X-ORG-ID: YOUR_ORG_ID" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "founders": [
    {
      "founder": {
        "fullName": "Andy",
        "linkedinUrl": "...",
        "locationCountry": "germany",
        "serialFounder": false,
        ...
      },
      "experiences": [...],
      "education": [...],
      "skills": [...],
      "profiles": [...],
      "signals": {
        "score": 75,
        "tags": ["experienced", "educated"],
        "strengths": {
          "items": [
            "Strong educational background",
            "Diverse skill set"
          ]
        },
        "risks": {
          "items": [
            "No fundraising track record"
          ]
        },
        "skillProfile": {
          "tech": 3,
          "product": 5,
          "business": 7,
          "operations": 6,
          "fundraising": 4
        }
      }
    }
  ]
}
```

### 2. Trigger Founder Enrichment

**Endpoint:** `POST /api/v1/pipelines/trigger`

**Description:** Triggers founder enrichment pipeline (forwards to datapipelines service on port 8001)

**Example Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/pipelines/trigger" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "X-ORG-ID: YOUR_ORG_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "https://superagent.co/",
    "org_id": "68515ce0e95f5962d46d3be3",
    "company_name": "SuperAgent",
    "domain": "superagent.co",
    "form_data": {
      "founder_name": "Jayaprasad Prabhakaran",
      "founder_linkedin": "https://linkedin.com/in/jayaprasad-prabhakaran"
    },
    "pipeline_types": ["founder"],
    "priority": "high"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Pipeline triggered successfully",
  "job_ids": {
    "founder": "job_12345"
  }
}
```

## Error Handling

### Deal Not Found
```json
{
  "detail": "Deal not found"
}
```

### Company Website Not Found
```json
{
  "detail": "Company website not found for deal"
}
```

### Pipeline Service Unavailable
```json
{
  "detail": "Pipeline service unavailable"
}
```

## Testing Workflow

1. **Create or find a deal** with a valid `company_website` field
2. **Trigger enrichment** using the pipeline endpoint with the company website as `company_id`
3. **Wait for processing** (enrichment may take several minutes)
4. **Fetch founder data** using the deal founders endpoint
5. **Verify response format** matches the expected camelCase structure

## Notes

- The `company_id` in the pipeline trigger should match the `company_website` field from the deal
- Founder data is stored using the company website URL as the identifier
- All response data is converted to camelCase for frontend compatibility
- The pipeline service must be running on port 8001 for enrichment to work
- Founder data includes experiences, education, skills, social profiles, and AI-generated signals
