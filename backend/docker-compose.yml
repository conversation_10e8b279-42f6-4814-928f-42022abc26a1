version: '3.8'

services:
  app:
    container_name: backend-app
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - TEST_MODE=false
      - PYTHONUNBUFFERED=1
    env_file:
      - .env.dev
    volumes:
      - ./app:/app/app
    restart: unless-stopped
    command: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - tractionx-net

  worker:
    container_name: backend-worker
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - WORKER_CONCURRENCY=2
      - PYTHONPATH=/app
      - TEST_MODE=true
      - PYTHONUNBUFFERED=1
      - DEBUG=1
      - WATCHDOG_ENABLED=1
    env_file:
      - .env.dev
    volumes:
      - ./app:/app/app:delegated  # Use delegated for better performance
    restart: unless-stopped
    command: bash -c "chmod +x /app/watch_worker.sh && /app/watch_worker.sh"
    networks:
      - tractionx-net

  # mongodb:
  #   image: mongo:latest
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongodb_data:/data/db
  #   restart: unless-stopped
  #   healthcheck:
  #     test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #     start_period: 10s



# volumes:
#   mongodb_data:
  # redis_data:

networks:
  tractionx-net:
    external: true
