"""
Pitch Parser Service Implementation

Handles LLM-based parsing of pitch decks and one-pagers to extract structured data.
"""

import json
import re
from typing import Any, Dict, List, Optional

from app.core.logging import get_logger
from app.services.base import BaseService
from app.services.openai.client import OpenAIClient
from app.services.pitch_parser.interface import PitchParserInterface

logger = get_logger(__name__)


class PitchParserService(BaseService, PitchParserInterface):
    """LLM-based pitch parser service using OpenAI."""

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.openai_client = None

    async def initialize(self) -> None:
        """Initialize the pitch parser service."""
        try:
            self.openai_client = OpenAIClient()
            self.logger.info("Pitch parser service initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize pitch parser service: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        try:
            self.openai_client = None
            self.logger.info("Pitch parser service cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during pitch parser service cleanup: {str(e)}")

    async def parse_pitch_content(
        self,
        text_content: str,
        pitch_type: str,
        pages: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Parse pitch content using LLM to extract structured data."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create the parsing prompt based on pitch type
            prompt = self._create_parsing_prompt(text_content, pitch_type, pages)

            self.logger.info(
                f"Starting pitch content parsing for {pitch_type} with {len(pages)} pages"
            )

            # Call OpenAI to parse the content with increased timeout for complex parsing
            response = await self.openai_client.create_chat_completion(
                messages=[
                    {"role": "system", "content": self._get_system_prompt(pitch_type)},
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.0,  # Low temperature for consistent extraction
                max_tokens=3000,  # Increased for complex pitch parsing
            )

            # Extract content from response
            content = self.openai_client.extract_content_from_completion(response)

            # Parse the JSON response
            parsed_data = self._parse_llm_response(content)

            # Add metadata
            parsed_data["parsing_metadata"] = {
                "pitch_type": pitch_type,
                "page_count": len(pages),
                "text_length": len(text_content),
                "model_used": "gpt-4",
                "confidence_score": self._calculate_confidence_score(parsed_data),
                "processing_time_ms": self.openai_client.extract_performance_metadata(
                    response
                ).get("response_time_ms", 0),
            }

            self.logger.info(
                f"Successfully parsed {pitch_type} content with confidence score: {parsed_data['parsing_metadata']['confidence_score']}"
            )
            return parsed_data

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(f"Timeout error parsing pitch content: {error_msg}")
                error_msg = "Request timed out during pitch parsing. Please try again."
            else:
                self.logger.error(f"Error parsing pitch content: {error_msg}")

            # Return empty structure with error
            return {
                "company_name": None,
                "company_website": None,
                "tagline": None,
                "ask_amount": None,
                "team_summary": None,
                "traction": None,
                "market_size": None,
                "sector": [],
                "founders": [],
                "source_page_map": {},
                "parsing_metadata": {
                    "error": error_msg,
                    "pitch_type": pitch_type,
                    "confidence_score": 0.0,
                    "page_count": len(pages),
                    "text_length": len(text_content),
                },
            }

    async def generate_markdown_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Generate a clean markdown summary for investor view."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create markdown generation prompt
            prompt = self._create_markdown_prompt(parsed_data, text_content, pitch_type)

            self.logger.info(f"Starting markdown summary generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating clean, professional markdown summaries of startup pitches for investors. Create well-structured, scannable summaries with clear headers and bullet points.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.3,
                max_tokens=4000,  # Increased for comprehensive summaries
            )

            # Extract content from response
            markdown_content = self.openai_client.extract_content_from_completion(
                response
            )

            # Add metadata footer
            markdown_content += f"\n\n---\n*Generated from {pitch_type} • {parsed_data.get('parsing_metadata', {}).get('page_count', 'Unknown')} pages*"

            self.logger.info(
                f"Successfully generated markdown summary for {pitch_type}"
            )
            return markdown_content

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating markdown summary: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating markdown summary: {error_msg}")

            # Return basic markdown with available data
            return self._create_fallback_markdown(parsed_data, pitch_type)

    async def generate_short_description(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Generate a concise short description (1-2 sentences) for the deal."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create short description prompt
            prompt = self._create_short_description_prompt(
                parsed_data, text_content, pitch_type
            )

            self.logger.info(f"Starting short description generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating concise, compelling descriptions of startups for investors. Create a 1-2 sentence description that captures the essence of the company, its value proposition, and key differentiators.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.3,
                max_tokens=300,  # Increased for better descriptions
            )

            # Extract content from response
            short_description = self.openai_client.extract_content_from_completion(
                response
            )

            # Clean up the description
            short_description = short_description.strip()
            if short_description.startswith('"') and short_description.endswith('"'):
                short_description = short_description[1:-1]

            self.logger.info(
                f"Successfully generated short description for {pitch_type}"
            )
            return short_description

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating short description: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating short description: {error_msg}")

            # Return fallback description
            return self._create_fallback_short_description(parsed_data, pitch_type)

    async def generate_executive_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Generate a comprehensive executive summary in Sequoia style."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create executive summary prompt
            prompt = self._create_executive_summary_prompt(
                parsed_data, text_content, pitch_type
            )

            self.logger.info(f"Starting executive summary generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert investment analyst at a top-tier VC firm like Sequoia Capital. Create a comprehensive executive summary that follows the highest standards of investment analysis. Focus on clarity, insight, and actionable intelligence for investors.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.2,
                max_tokens=4000,  # Increased for comprehensive executive summaries
            )

            # Extract content from response
            executive_summary = self.openai_client.extract_content_from_completion(
                response
            )

            self.logger.info(
                f"Successfully generated executive summary for {pitch_type}"
            )
            return executive_summary

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating executive summary: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating executive summary: {error_msg}")

            # Return fallback executive summary
            return self._create_fallback_executive_summary(parsed_data, pitch_type)

    def _get_system_prompt(self, pitch_type: str) -> str:
        """Get the system prompt for the LLM based on pitch type."""
        base_prompt = """You are an expert startup pitch analyzer. Extract structured information from pitch content and return it as valid JSON.

            IMPORTANT: Always return valid JSON with ALL required fields, even if some information is not found (use null for missing values).

            Required JSON structure:
            {
            "company_name": "string or null",
            "company_website": "string or null",
            "tagline": "string or null", 
            "ask_amount": "string or null",
            "team_summary": "string or null",
            "traction": "string or null",
            "market_size": "string or null",
            "sector": ["array", "of", "strings"],
            "founders": [
                {
                    "name": "string or null",
                    "role": ["array", "of", "roles"],
                    "linkedin": "string or null",
                    "email": "string or null",
                    "serial_founder": boolean,
                    "experience": [
                        {
                            "company": "string",
                            "position": "string",
                            "duration": "string",
                            "description": "string"
                        }
                    ],
                    "skills": ["array", "of", "skills"],
                    "education": [
                        {
                            "institution": "string",
                            "degree": "string",
                            "field": "string",
                            "year": "string"
                        }
                    ],
                    "achievements": ["array", "of", "achievements"]
                }
            ],
            "source_page_map": {
                "company_name": 1,
                "company_website": 1,
                "ask_amount": 8,
                "founders": [2, 3]
            }
            }

            FOUNDER EXTRACTION GUIDELINES:
            - Look for team/founder sections, about us, leadership slides
            - Extract full names, titles/roles, LinkedIn profiles, emails
            - Identify serial founders (those with previous startup experience)
            - Extract relevant work experience, education, skills, and achievements
            - If founder info is limited, extract what's available and use null for missing fields
            - For roles, include both current role and any mentioned previous roles
            - Look for patterns like "CEO & Co-founder", "CTO", "Head of Engineering", etc."""

        if pitch_type == "one_pager":
            return (
                base_prompt
                + "\n\nThis is a ONE-PAGER document (1-2 pages). Information will be condensed and may be in paragraph form rather than distinct slides."
            )
        else:
            return (
                base_prompt
                + "\n\nThis is a PITCH DECK (multiple slides). Look for information across different slides/sections."
            )

    def _create_parsing_prompt(
        self, text_content: str, pitch_type: str, pages: List[Dict[str, Any]]
    ) -> str:
        """Create the parsing prompt for the LLM."""
        prompt = f"""Analyze this {pitch_type} and extract the following information:

                    1. Company name
                    2. Company website (look for URLs, contact info, footer, also there is a change the website give is of the product, not the company. So check for things like v1 or app.com or something like that. Make sure you get the company website.)
                    3. Tagline/description
                    4. Funding ask amount (e.g., "$1.5M", "Series A $5M")
                    5. Team summary (key founders and their backgrounds)
                    6. Traction (metrics, revenue, customers, growth)
                    7. Market size (TAM, SAM, market opportunity)
                    8. Sector/industry (as array of strings)
                    9. Founders (detailed information for each founder)
                    10. Source page map (which page each key info was found on)

                    FOUNDER EXTRACTION FOCUS:
                    - Look for team/founder sections, about us, leadership slides
                    - Extract full names, titles/roles, LinkedIn profiles, emails
                    - Identify serial founders (those with previous startup experience)
                    - Extract relevant work experience, education, skills, and achievements
                    - Pay attention to patterns like "CEO & Co-founder", "CTO", "Head of Engineering"
                    - Look for educational background, previous companies, and notable achievements
                    - If founder info is limited, extract what's available

                    CONTENT TO ANALYZE:
                    {text_content[:8000]}  # Limit content to avoid token limits

                    PAGE BREAKDOWN:
                    """

        for page in pages[:10]:  # Limit to first 10 pages
            prompt += f"Page {page['page_number']}: {page['text'][:500]}...\n\n"

        prompt += "\nReturn the extracted information as valid JSON following the required structure."

        return prompt

    def _create_markdown_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the markdown generation prompt."""
        return f"""Create a professional markdown summary for investors based on this parsed pitch data:

PARSED DATA:
{json.dumps(parsed_data, indent=2)}

        PITCH TYPE: {pitch_type}

        Create a clean, scannable markdown summary with:
        - Clear headers (## Company, ## Team, ## Traction, etc.)
        - Bullet points for key information
        - Professional tone suitable for investor review
        - Page references where available (e.g., "Ask: $1.5M (Page 8)")
        - Company website and contact information, also there is a change the website give is of the product, not the company. So check for things like v1 or app.com or something like that. Make sure you get the company website.
        - Detailed founder information including roles, experience, and achievements

        FOUNDER SECTION GUIDELINES:
        - List each founder with their name, role, and key background
        - Highlight serial founders and their previous startup experience
        - Include relevant education, skills, and notable achievements
        - Mention LinkedIn profiles and contact information if available
        - Focus on experience that's relevant to the current venture

        Focus on the most important information for investment decisions."""

    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """Parse the LLM response and extract JSON."""
        try:
            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # If no JSON found, try parsing the whole response
                return json.loads(response_content)

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse LLM JSON response: {str(e)}")
            # Return empty structure
            return {
                "company_name": None,
                "company_website": None,
                "tagline": None,
                "ask_amount": None,
                "team_summary": None,
                "traction": None,
                "market_size": None,
                "sector": [],
                "founders": [],
                "source_page_map": {},
            }

    def _calculate_confidence_score(self, parsed_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on how much data was extracted."""
        total_fields = 9  # company_name, company_website, tagline, ask_amount, team_summary, traction, market_size, sector, founders
        filled_fields = 0

        key_fields = [
            "company_name",
            "company_website",
            "tagline",
            "ask_amount",
            "team_summary",
            "traction",
            "market_size",
            "sector",
            "founders",
        ]

        for field in key_fields:
            value = parsed_data.get(field)
            if (
                value
                and (isinstance(value, str) and value.strip())
                or (isinstance(value, list) and len(value) > 0)
            ):
                filled_fields += 1

        return round(filled_fields / total_fields, 2)

    def _create_fallback_markdown(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> str:
        """Create a basic markdown summary when LLM generation fails."""
        markdown = "# Pitch Summary\n\n"

        if parsed_data.get("company_name"):
            markdown += f"**Company:** {parsed_data['company_name']}\n\n"

        if parsed_data.get("company_website"):
            markdown += f"**Website:** {parsed_data['company_website']}\n\n"

        if parsed_data.get("tagline"):
            markdown += f"**Tagline:** {parsed_data['tagline']}\n\n"

        if parsed_data.get("ask_amount"):
            markdown += f"**Ask:** {parsed_data['ask_amount']}\n\n"

        if parsed_data.get("sector"):
            markdown += f"**Sector:** {', '.join(parsed_data['sector'])}\n\n"

        # Add founder information
        founders = parsed_data.get("founders", [])
        if founders:
            markdown += "## Founders\n\n"
            for founder in founders:
                markdown += f"**{founder.get('name', 'Unknown')}**"
                if founder.get("role"):
                    roles = (
                        founder["role"]
                        if isinstance(founder["role"], list)
                        else [founder["role"]]
                    )
                    markdown += f" - {', '.join(roles)}"
                markdown += "\n"

                if founder.get("serial_founder"):
                    markdown += "  - Serial founder\n"

                if founder.get("experience"):
                    markdown += "  - **Experience:**\n"
                    for exp in founder["experience"][:3]:  # Show first 3 experiences
                        if isinstance(exp, dict):
                            company = exp.get("company", "Unknown")
                            position = exp.get("position", "")
                            markdown += f"    - {position} at {company}\n"

                if founder.get("education"):
                    markdown += "  - **Education:**\n"
                    for edu in founder["education"][
                        :2
                    ]:  # Show first 2 education entries
                        if isinstance(edu, dict):
                            institution = edu.get("institution", "Unknown")
                            degree = edu.get("degree", "")
                            markdown += f"    - {degree} from {institution}\n"

                if founder.get("linkedin"):
                    markdown += f"  - LinkedIn: {founder['linkedin']}\n"

                markdown += "\n"
        elif parsed_data.get("team_summary"):
            markdown += f"## Team\n{parsed_data['team_summary']}\n\n"

        if parsed_data.get("traction"):
            markdown += f"## Traction\n{parsed_data['traction']}\n\n"

        if parsed_data.get("market_size"):
            markdown += f"## Market\n{parsed_data['market_size']}\n\n"

        markdown += f"\n---\n*Generated from {pitch_type} • Fallback summary*"

        return markdown

    def _create_short_description_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the short description generation prompt."""
        return f"""Create a compelling 1-2 sentence description for this {pitch_type} that would appear in a deal database or investor dashboard.

PARSED DATA:
{json.dumps(parsed_data, indent=2)}

PITCH TYPE: {pitch_type}

Requirements:
- 1-2 sentences maximum
- Capture the company's core value proposition
- Include key differentiators or unique aspects
- Professional tone suitable for investors
- Focus on what makes this company special

Create a concise, compelling description that would make an investor want to learn more."""

    def _create_fallback_short_description(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> str:
        """Create a fallback short description when LLM generation fails."""
        company_name = parsed_data.get("company_name", f"Company from {pitch_type}")
        tagline = parsed_data.get("tagline", "")
        sector = parsed_data.get("sector", [])

        if tagline:
            return f"{company_name}: {tagline}"
        elif sector:
            return f"{company_name} is a {', '.join(sector[:2])} company."
        else:
            return f"{company_name} is an innovative startup seeking investment."

    def _create_executive_summary_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the executive summary generation prompt."""
        return f"""Create a comprehensive executive summary for this {pitch_type} following the highest standards of investment analysis.

PARSED DATA:
{json.dumps(parsed_data, indent=2)}

PITCH TYPE: {pitch_type}

Create a professional executive summary that includes:

1. **Company Overview** (2-3 sentences)
   - What the company does
   - Core value proposition
   - Target market
   - Company website and contact information

2. **Market Opportunity** (2-3 sentences)
   - Market size and growth potential
   - Problem being solved
   - Market dynamics

3. **Business Model** (2-3 sentences)
   - Revenue model
   - Key partnerships
   - Competitive advantages

4. **Team & Traction** (3-4 sentences)
   - Key team members and their backgrounds
   - Highlight serial founders and relevant experience
   - Current traction and metrics
   - Growth indicators

5. **Financial Highlights** (1-2 sentences)
   - Funding ask and use of funds
   - Key financial metrics if available

6. **Investment Thesis** (2-3 sentences)
   - Why this is an attractive investment
   - Key risks and mitigations
   - Expected outcomes

TEAM SECTION GUIDELINES:
- Highlight each founder's key qualifications and experience
- Emphasize serial founders and their previous startup success
- Include relevant educational background and industry experience
- Focus on experience that directly relates to the current venture
- Mention any notable achievements or recognitions

Format as a professional executive summary with clear sections and bullet points where appropriate. Use markdown formatting for structure.

Focus on providing actionable intelligence for investors, following the style of top-tier VC firms like Sequoia Capital."""

    def _create_fallback_executive_summary(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> str:
        """Create a fallback executive summary when LLM generation fails."""
        company_name = parsed_data.get("company_name", f"Company from {pitch_type}")
        company_website = parsed_data.get("company_website", "")
        tagline = parsed_data.get("tagline", "")
        sector = parsed_data.get("sector", [])
        team_summary = parsed_data.get("team_summary", "")
        traction = parsed_data.get("traction", "")
        market_size = parsed_data.get("market_size", "")
        ask_amount = parsed_data.get("ask_amount", "")
        founders = parsed_data.get("founders", [])

        summary = "# Executive Summary\n\n"
        summary += "## Company Overview\n\n"
        summary += f"{company_name}"
        if tagline:
            summary += f": {tagline}"
        summary += "\n\n"

        if company_website:
            summary += f"**Website:** {company_website}\n\n"

        if sector:
            summary += f"**Sector:** {', '.join(sector)}\n\n"

        if market_size:
            summary += f"## Market Opportunity\n\n{market_size}\n\n"

        # Add founder information
        if founders:
            summary += "## Team\n\n"
            for founder in founders:
                summary += f"**{founder.get('name', 'Unknown')}**"
                if founder.get("role"):
                    roles = (
                        founder["role"]
                        if isinstance(founder["role"], list)
                        else [founder["role"]]
                    )
                    summary += f" - {', '.join(roles)}"
                summary += "\n"

                if founder.get("serial_founder"):
                    summary += "  - Serial founder with previous startup experience\n"

                if founder.get("experience"):
                    summary += "  - **Key Experience:**\n"
                    for exp in founder["experience"][:2]:  # Show first 2 experiences
                        if isinstance(exp, dict):
                            company = exp.get("company", "Unknown")
                            position = exp.get("position", "")
                            summary += f"    - {position} at {company}\n"

                if founder.get("education"):
                    summary += "  - **Education:**\n"
                    for edu in founder["education"][:1]:  # Show first education entry
                        if isinstance(edu, dict):
                            institution = edu.get("institution", "Unknown")
                            degree = edu.get("degree", "")
                            summary += f"    - {degree} from {institution}\n"

                summary += "\n"
        elif team_summary:
            summary += f"## Team\n\n{team_summary}\n\n"

        if traction:
            summary += f"## Traction\n\n{traction}\n\n"

        if ask_amount:
            summary += f"## Financial Highlights\n\n**Funding Ask:** {ask_amount}\n\n"

        summary += f"---\n*Generated from {pitch_type} • Fallback executive summary*"

        return summary
