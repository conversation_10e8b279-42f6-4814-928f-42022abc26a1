"""
Upload endpoints for profile pictures, organization logos, pitch decks, and other assets.
"""

import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.user import User
from app.services.factory import get_deal_service, get_file_service, get_queue_service
from app.services.queue.interfaces import JobPriority, QueueType

logger = get_logger(__name__)

router = APIRouter(prefix="/uploads", tags=["Uploads"])


class GenerateUploadUrlRequest(BaseModel):
    """Request model for generating presigned upload URL for assets."""

    file_type: str = Field(..., description="Type of file (avatar, logo)")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")


class GenerateUploadUrlResponse(BaseModel):
    """Response model for presigned upload URL."""

    presigned_url: str = Field(..., description="Presigned URL for upload")
    public_url: str = Field(..., description="Public URL for accessing the file")
    s3_key: str = Field(..., description="S3 key for the uploaded file")
    expires_in: int = Field(..., description="URL expiration time in seconds")


class PitchUploadRequest(BaseModel):
    """Request model for pitch upload."""

    type: str = Field(
        default="deck", description="Type of pitch: 'deck' or 'one_pager'"
    )
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")


class PitchUploadResponse(BaseModel):
    """Response model for pitch upload."""

    temp_id: str = Field(..., description="Temporary ID for the upload")
    presigned_url: str = Field(..., description="Presigned URL for upload")
    s3_key: str = Field(..., description="S3 key for the uploaded file")
    status: str = Field(default="pending", description="Upload status")
    expires_in: int = Field(..., description="URL expiration time in seconds")


class ConfirmPitchUploadRequest(BaseModel):
    """Request model for confirming pitch upload."""

    temp_id: str = Field(..., description="Temporary ID for the upload")


class ConfirmPitchUploadResponse(BaseModel):
    """Response model for pitch upload confirmation."""

    temp_id: str = Field(..., description="Temporary ID for the upload")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Status message")


@router.post("/generate-presigned-url", response_model=GenerateUploadUrlResponse)
async def generate_presigned_upload_url(
    request: GenerateUploadUrlRequest,
    current_user: User = Depends(get_current_user),
    file_service=Depends(get_file_service),
) -> GenerateUploadUrlResponse:
    """
    Generate a presigned URL for uploading profile pictures, logos, etc.

    This endpoint creates presigned URLs for uploading user avatars and organization logos
    directly to S3. The files are stored in a public bucket with unguessable paths.
    """
    try:
        # Validate file type
        allowed_types = ["avatar", "logo", "pitch_deck"]
        if request.file_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed types: {allowed_types}",
            )

        # Validate content type based on file type
        if request.file_type in ["avatar", "logo"]:
            allowed_content_types = [
                "image/jpeg",
                "image/png",
                "image/webp",
                "image/svg+xml",
            ]
        elif request.file_type == "pitch_deck":
            allowed_content_types = [
                "application/pdf",
                "application/vnd.ms-powerpoint",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ]
        else:
            allowed_content_types = []

        if request.content_type not in allowed_content_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid content type. Allowed types: {allowed_content_types}",
            )

        # Validate file size based on file type
        if request.file_type == "logo":
            max_size = 10 * 1024 * 1024  # 10MB for logos
        elif request.file_type == "avatar":
            max_size = 5 * 1024 * 1024  # 5MB for avatars
        elif request.file_type == "pitch_deck":
            max_size = 50 * 1024 * 1024  # 50MB for pitch decks
        else:
            max_size = 5 * 1024 * 1024  # Default 5MB

        if request.file_size > max_size:
            max_mb = max_size // (1024 * 1024)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {max_mb}MB",
            )

        # Generate unique S3 key based on file type
        file_extension = request.filename.split(".")[-1].lower()
        unique_id = str(uuid.uuid4())

        if request.file_type == "pitch_deck":
            s3_key = (
                f"deals/pitch-decks/{current_user.org_id}/{unique_id}.{file_extension}"
            )
        else:
            s3_key = f"assets/{request.file_type}s/{current_user.org_id}/{unique_id}.{file_extension}"

        # Generate presigned URL for PUT operation
        presigned_url = file_service.s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": settings.S3_BUCKET_ASSETS,  # Use assets bucket
                "Key": s3_key,
                "ContentType": request.content_type,
                "ContentLength": request.file_size,
                "ACL": "public-read",  # Make files publicly readable
            },
            ExpiresIn=settings.S3_PRESIGNED_URL_EXPIRY,
        )

        # Generate public URL for accessing the file
        public_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"

        logger.info(
            f"Generated presigned upload URL for {request.file_type} by user {current_user.email}"
        )

        return GenerateUploadUrlResponse(
            presigned_url=presigned_url,
            public_url=public_url,
            s3_key=s3_key,
            expires_in=settings.S3_PRESIGNED_URL_EXPIRY,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned upload URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate upload URL",
        )


@router.post("/pitch", response_model=PitchUploadResponse)
async def upload_pitch(
    request: PitchUploadRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    file_service=Depends(get_file_service),
) -> PitchUploadResponse:
    """
    Upload a pitch deck or one-pager for AI parsing.

    This endpoint handles pitch deck uploads by:
    1. Generating a temporary ID for the upload
    2. Creating a presigned URL for S3 upload to temp location
    3. The file will be processed after upload confirmation

    Supports both multi-slide decks and one-pagers.
    """
    try:
        org_id, _ = org_context

        # Validate file type - only PDF allowed for pitches
        if request.content_type != "application/pdf":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF files are allowed for pitch uploads",
            )

        # Validate pitch type
        if request.type not in ["deck", "one_pager"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Pitch type must be 'deck' or 'one_pager'",
            )

        # Validate file size (50MB max for pitch decks)
        max_size = 50 * 1024 * 1024  # 50MB
        if request.file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File too large. Maximum size: 50MB",
            )

        # Generate temporary ID
        temp_id = str(uuid.uuid4())

        # Generate S3 key for temporary storage
        file_extension = request.filename.split(".")[-1].lower()
        s3_key = f"deals/{org_id}/temp/{temp_id}/pitch_upload.{file_extension}"

        # Generate presigned URL for PUT operation
        presigned_url = file_service.s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": settings.S3_BUCKET_ASSETS,
                "Key": s3_key,
                "ContentType": request.content_type,
                "ContentLength": request.file_size,
            },
            ExpiresIn=settings.S3_PRESIGNED_URL_EXPIRY,
        )

        logger.info(
            f"Generated presigned URL for pitch upload temp_id {temp_id} by user {current_user.email}"
        )

        return PitchUploadResponse(
            temp_id=temp_id,
            presigned_url=presigned_url,
            s3_key=s3_key,
            status="pending",
            expires_in=settings.S3_PRESIGNED_URL_EXPIRY,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading pitch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload pitch",
        )


@router.post("/pitch/confirm", response_model=ConfirmPitchUploadResponse)
async def confirm_pitch_upload(
    request: ConfirmPitchUploadRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    queue_service=Depends(get_queue_service),
) -> ConfirmPitchUploadResponse:
    """
    Confirm that a pitch file has been uploaded and trigger processing.

    This endpoint is called after the file has been successfully uploaded to S3
    to trigger the background processing job.
    """
    try:
        org_id, _ = org_context

        # Queue the parsing job
        await queue_service.enqueue_job(
            job_type="parse_pitch_upload",
            payload={
                "temp_id": request.temp_id,
                "org_id": org_id,
                "user_id": str(current_user.id),
                "user_email": current_user.email,
            },
            priority=JobPriority.HIGH,  # High priority for user uploads
            queue_type=QueueType.DEFAULT,
        )

        logger.info(
            f"Queued pitch parsing job for temp_id {request.temp_id} by user {current_user.email}"
        )

        return ConfirmPitchUploadResponse(
            temp_id=request.temp_id,
            status="queued",
            message="Pitch upload confirmed and processing queued",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming pitch upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to confirm pitch upload",
        )


# NEW: Endpoints for existing deals
@router.post("/deals/{deal_id}/pitch", response_model=PitchUploadResponse)
async def upload_pitch_for_existing_deal(
    deal_id: str,
    request: PitchUploadRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    file_service=Depends(get_file_service),
    deal_service=Depends(get_deal_service),
) -> PitchUploadResponse:
    """
    Upload a pitch deck for an existing deal.

    This endpoint handles pitch deck uploads for existing deals by:
    1. Validating the deal exists and user has access
    2. Generating a temporary ID for the upload
    3. Creating a presigned URL for S3 upload to temp location
    4. The file will be processed after upload confirmation
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            from bson import ObjectId

            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Validate file type - only PDF allowed for pitches
        if request.content_type != "application/pdf":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF files are allowed for pitch uploads",
            )

        # Validate pitch type
        if request.type not in ["deck", "one_pager"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Pitch type must be 'deck' or 'one_pager'",
            )

        # Validate file size (50MB max for pitch decks)
        max_size = 50 * 1024 * 1024  # 50MB
        if request.file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File too large. Maximum size: 50MB",
            )

        # Generate temporary ID
        temp_id = str(uuid.uuid4())

        # Generate S3 key for temporary storage
        file_extension = request.filename.split(".")[-1].lower()
        s3_key = f"deals/{org_id}/temp/{temp_id}/pitch_upload.{file_extension}"

        # Generate presigned URL for PUT operation
        presigned_url = file_service.s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": settings.S3_BUCKET_ASSETS,
                "Key": s3_key,
                "ContentType": request.content_type,
                "ContentLength": request.file_size,
            },
            ExpiresIn=settings.S3_PRESIGNED_URL_EXPIRY,
        )

        logger.info(
            f"Generated presigned URL for pitch upload temp_id {temp_id} for deal {deal_id} by user {current_user.email}"
        )

        return PitchUploadResponse(
            temp_id=temp_id,
            presigned_url=presigned_url,
            s3_key=s3_key,
            status="pending",
            expires_in=settings.S3_PRESIGNED_URL_EXPIRY,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading pitch for deal {deal_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload pitch",
        )


@router.post(
    "/deals/{deal_id}/pitch/confirm", response_model=ConfirmPitchUploadResponse
)
async def confirm_pitch_upload_for_existing_deal(
    deal_id: str,
    request: ConfirmPitchUploadRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    queue_service=Depends(get_queue_service),
    deal_service=Depends(get_deal_service),
) -> ConfirmPitchUploadResponse:
    """
    Confirm that a pitch file has been uploaded for an existing deal and trigger processing.

    This endpoint is called after the file has been successfully uploaded to S3
    to trigger the background processing job for an existing deal.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            from bson import ObjectId

            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Queue the parsing job with deal_id included
        await queue_service.enqueue_job(
            job_type="parse_pitch_upload_existing_deal",  # New job type for existing deals
            payload={
                "temp_id": request.temp_id,
                "deal_id": deal_id,  # Include deal_id for existing deal processing
                "org_id": org_id,
                "user_id": str(current_user.id),
                "user_email": current_user.email,
            },
            priority=JobPriority.HIGH,  # High priority for user uploads
            queue_type=QueueType.DEFAULT,
        )

        logger.info(
            f"Queued pitch parsing job for existing deal {deal_id}, temp_id {request.temp_id} by user {current_user.email}"
        )

        return ConfirmPitchUploadResponse(
            temp_id=request.temp_id,
            status="queued",
            message="Pitch upload confirmed and processing queued for existing deal",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming pitch upload for deal {deal_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to confirm pitch upload",
        )


@router.delete("/asset/{s3_key:path}")
async def delete_asset(
    s3_key: str,
    current_user: User = Depends(get_current_user),
    file_service=Depends(get_file_service),
) -> dict:
    """
    Delete an uploaded asset (avatar, logo, etc.) from S3.

    This endpoint allows users to delete their uploaded assets. It validates
    that the user has permission to delete the asset based on the S3 key path.
    """
    try:
        # Validate that the user can delete this asset
        # S3 key format: assets/{type}s/{org_id}/{unique_id}.{ext}
        key_parts = s3_key.split("/")
        if len(key_parts) < 3 or key_parts[0] != "assets":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid asset key"
            )

        asset_org_id = key_parts[2]
        if asset_org_id != str(current_user.org_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to delete this asset",
            )

        # Delete from S3
        file_service.s3_client.delete_object(
            Bucket=settings.S3_BUCKET_ASSETS, Key=s3_key
        )

        logger.info(f"Deleted asset {s3_key} by user {current_user.email}")

        return {"message": "Asset deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting asset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete asset",
        )
