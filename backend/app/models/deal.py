from datetime import datetime, timezone
from enum import Enum
from typing import Annotated, Any, Dict, List, Optional, Union

from bson import ObjectId
from pydantic import ConfigDict, Field

from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.user import User
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class DealStatus(str, Enum):
    """Status of a deal in the investor's pipeline."""

    NEW = "new"
    TRIAGE = "triage"
    REVIEWED = "reviewed"
    EXCLUDED = "excluded"
    REJECTED = "rejected"
    APPROVED = "approved"
    NEGOTIATING = "negotiating"
    CLOSED = "closed"


class Founder(TractionXModel):
    name: Optional[str] = Field(None, description="Founder's name")
    role: Optional[List[str]] = Field(
        default_factory=list, description="Founder's role"
    )
    linkedin: Optional[str] = Field(None, description="Founder's LinkedIn profile")
    email: Optional[str] = Field(None, description="Founder's email")
    serial_founder: bool = Field(
        default=False, description="Whether the founder is a serial founder"
    )
    profile_picture: Optional[str] = Field(
        None, description="Founder's profile picture"
    )
    experience: Optional[List[Dict[str, Any]]] = Field(
        None, description="Founder's experience"
    )
    skills: Optional[List[str]] = Field(None, description="Founder's skills")
    education: Optional[List[Dict[str, Any]]] = Field(
        None, description="Founder's education"
    )
    achievements: Optional[List[str]] = Field(
        None, description="Founder's achievements"
    )


class Deal(TractionXModel):
    """
    Represents a deal created from a form submission.
    Contains core information needed for dashboard display and deal tracking.
    """

    model_config = ConfigDict(
        extra_model_config={
            "json_schema_extra": {
                "example": {
                    "company_name": "Acme Corp",
                    "stage": "Series A",
                    "sector": ["Technology", "SaaS"],
                    "status": "new",
                    "form_id": "form123",
                    "submission_ids": ["sub123"],
                    "exclusion_filter_result": {"excluded": False, "reason": None},
                    "scoring": {
                        "thesis": {
                            "total_score": 85,
                            "normalized_score": 85.0,
                            "question_scores": {},
                            "bonus_scores": {},
                            "thesis_matches": ["thesis1", "thesis2"],
                        },
                        "founders": {
                            "total_score": 90,
                            "normalized_score": 90.0,
                            "ai_analysis": "Strong founding team with relevant experience",
                        },
                        "market": {
                            "total_score": 80,
                            "normalized_score": 80.0,
                            "ai_analysis": "Growing market with strong potential",
                        },
                    },
                    "notes": "Promising early-stage SaaS company",
                    "tags": ["AI", "B2B"],
                    "timeline": [
                        {
                            "date": "2024-03-20T10:00:00Z",
                            "event": "Initial submission",
                            "notes": "Submitted through website",
                        }
                    ],
                }
            }
        }
    )  # type: ignore

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: Annotated[ObjectIdField, populate_reference(Organization)]
    form_id: Optional[Annotated[ObjectIdField, populate_reference("Form")]] = None
    submission_ids: Optional[List[ObjectIdField]] = Field(
        default_factory=list,
        description="List of submission IDs associated with this deal",
    )

    # Core fields extracted from submissions
    company_name: Optional[str] = Field(
        None, description="Company name from submission"
    )
    stage: Optional[str] = Field(None, description="Company stage from submission")
    sector: Optional[Union[str, List[str]]] = Field(
        None, description="Company sector(s) from submission"
    )
    company_website: Optional[str] = Field(
        None, description="Company website from submission"
    )

    short_description: Optional[str] = Field(
        None, description="Short description from submission"
    )

    # Deal tracking fields
    status: DealStatus = Field(
        default=DealStatus.NEW, description="Current status of the deal"
    )
    exclusion_filter_result: Optional[Dict[str, Any]] = Field(
        None, description="Result of any exclusion filters applied"
    )
    scoring: Optional[Dict[str, Any]] = Field(
        None,
        description="Comprehensive scoring results with thesis, founders, and market analysis",
    )

    # Invite tracking fields (PRD 1)
    invited_email: Optional[str] = Field(
        None, description="Email address of invited startup contact"
    )
    invite_status: Optional[str] = Field(
        None, description="Status of invite: invited, sent, opened, clicked, submitted"
    )
    invite_sent_at: Optional[int] = Field(
        None, description="Timestamp when invite was sent"
    )
    pitch_deck_url: Optional[str] = Field(
        None, description="S3 URL of uploaded pitch deck"
    )
    context_block_url: Optional[str] = Field(
        None, description="S3 URL of generated context block for AI"
    )
    executive_summary_url: Optional[str] = Field(
        None, description="S3 URL of generated executive summary"
    )

    # Enrichment tracking
    enrichment_status: Optional[str] = Field(
        None, description="Status of AI enrichment (pending/completed/failed)"
    )
    enriched_data: Optional[Dict[str, Any]] = Field(
        None, description="AI-generated enrichment data"
    )
    enrichment_job_id: Optional[str] = Field(
        None, description="ID of the enrichment job"
    )

    # Error tracking for async processing
    exclusion_filter_error: Optional[str] = Field(
        None, description="Error during exclusion filter processing"
    )
    thesis_matching_error: Optional[str] = Field(
        None, description="Error during thesis matching"
    )
    enrichment_error: Optional[str] = Field(
        None, description="Error during enrichment processing"
    )
    founders: List[Founder] = Field(
        default_factory=list, description="Founders of the company"
    )
    created_by: Annotated[ObjectIdField, populate_reference(User)] = Field(
        default_factory=lambda: PyObjectId("680fc5c6980ce9ae38ac6729"),
        description="User who created the deal",
    )

    # Assignment tracking
    assigned_user_ids: List[Annotated[ObjectIdField, populate_reference(User)]] = Field(
        default_factory=list, description="List of users assigned to this deal"
    )

    # Optional tracking fields
    notes: Optional[str] = Field(None, description="Notes about the deal")
    tags: List[str] = Field(
        default_factory=list, description="Tags for categorizing the deal"
    )
    timeline: List[Dict[str, Any]] = Field(
        default_factory=list, description="Timeline of deal events"
    )

    # Favourites tracking
    favourites: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of favourites: each is a dict with user_id and timestamp",
    )

    # Metadata
    # created_by: Annotated[ObjectIdField, populate_reference(User)] = Field(..., description="User who created the deal")
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    def add_submission(self, submission_id: Union[str, ObjectId]) -> None:
        """Add a submission ID to the deal's submission history."""
        if isinstance(submission_id, str):
            submission_id = ObjectId(submission_id)
        if self.submission_ids and submission_id not in self.submission_ids:
            self.submission_ids.append(PyObjectId(submission_id))
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def add_timeline_event(self, event: str, notes: Optional[str] = None) -> None:
        """Add an event to the deal's timeline."""
        self.timeline.append({
            "date": datetime.now(timezone.utc).isoformat(),
            "event": event,
            "notes": notes,
        })
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def update_status(
        self, new_status: DealStatus, notes: Optional[str] = None
    ) -> None:
        """Update the deal's status and add a timeline event."""
        self.status = new_status
        self.add_timeline_event(f"Status changed to {new_status}", notes)
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def add_tag(self, tag: str) -> None:
        """Add a tag to the deal if it doesn't exist."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the deal."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())
