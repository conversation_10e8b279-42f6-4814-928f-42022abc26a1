{"name": "tx-mvp", "version": "0.1.0", "private": true, "author": {"name": "Prasanna-TX", "url": "https://twitter.com/prasanna"}, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "turbo": "next dev --turbo", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:primitives": "grep -r \"import \\* as .*Primitive\" ./components/ui && echo \"❌ Legacy Primitive imports found\" || echo \"✅ No legacy Primitive imports found\"", "preview": "next build && next start", "postinstall": "prisma generate", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "export": "next build && next export"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@editorjs/code": "^2.8.0", "@editorjs/editorjs": "^2.26.5", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/paragraph": "^2.9.0", "@editorjs/table": "^2.2.1", "@hookform/resolvers": "^3.1.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^4.13.0", "@radix-ui/react-accessible-icon": "1.0.2", "@radix-ui/react-accordion": "1.1.1", "@radix-ui/react-alert-dialog": "1.0.3", "@radix-ui/react-aspect-ratio": "1.0.2", "@radix-ui/react-avatar": "1.0.2", "@radix-ui/react-checkbox": "1.0.3", "@radix-ui/react-collapsible": "1.0.2", "@radix-ui/react-context-menu": "2.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-hover-card": "1.0.5", "@radix-ui/react-label": "1.0.0", "@radix-ui/react-menubar": "1.0.2", "@radix-ui/react-navigation-menu": "1.1.2", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-progress": "1.0.2", "@radix-ui/react-radio-group": "1.1.2", "@radix-ui/react-scroll-area": "1.0.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "1.0.2", "@radix-ui/react-slider": "1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.0.2", "@radix-ui/react-tabs": "1.0.3", "@radix-ui/react-toggle": "1.0.2", "@radix-ui/react-toggle-group": "1.0.3", "@radix-ui/react-tooltip": "1.0.5", "@t3-oss/env-nextjs": "^0.2.2", "@tanstack/react-query": "^5.80.10", "@types/jspdf": "^1.3.3", "@types/react-beautiful-dnd": "^13.1.8", "@typescript-eslint/parser": "^5.59.0", "@vercel/analytics": "^1.0.0", "@vercel/og": "^0.0.21", "animejs": "^3.2.1", "class-variance-authority": "^0.7.1", "clsx": "^1.2.1", "cmdk": "^0.1.22", "concurrently": "^8.0.1", "critters": "^0.0.23", "date-fns": "^2.30.0", "framer-motion": "^12.16.0", "jspdf": "^3.0.1", "lucide-react": "^0.92.0", "next": "15.3.2", "next-auth": "^4.22.1", "next-themes": "^0.2.1", "nodemailer": "^6.9.1", "postmark": "^3.0.15", "prop-types": "^15.8.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-editor-js": "^2.1.0", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.5.2", "react-textarea-autosize": "^8.4.1", "react-use": "^17.6.0", "recharts": "^2.15.3", "sharp": "^0.31.3", "shiki": "^0.11.1", "sonner": "^2.0.5", "stripe": "^11.18.0", "tailwind-merge": "^1.12.0", "tailwindcss-animate": "^1.0.5", "zod": "^3.21.4"}, "devDependencies": {"@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@ianvs/prettier-plugin-sort-imports": "^3.7.2", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "@types/node": "^18.16.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.14", "eslint": "^8.39.0", "eslint-config-next": "13.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-tailwindcss": "^3.11.0", "husky": "^8.0.3", "mdast-util-toc": "^6.1.1", "postcss": "^8.4.23", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.1.13", "pretty-quick": "^3.1.3", "prisma": "^4.13.0", "rehype": "^12.0.1", "rehype-autolink-headings": "^6.1.1", "rehype-pretty-code": "^0.9.5", "rehype-slug": "^5.1.0", "remark": "^14.0.2", "remark-gfm": "^3.0.1", "tailwindcss": "^3.3.1", "typescript": "^5.2.2", "unist-util-visit": "^4.1.2"}}