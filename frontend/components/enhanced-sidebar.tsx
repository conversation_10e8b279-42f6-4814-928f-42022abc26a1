"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import {
  ChevronLeft,
  ChevronRight,
  Home,
  FileText,
  Target,
  Settings,
  BarChart3,
  Wand2
} from "lucide-react"

import { SidebarNavItem } from "@/types"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { useAuth } from "@/lib/auth-context"
import { UserAvatar } from "@/components/user-avatar"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface EnhancedSidebarProps {
  items: SidebarNavItem[]
  isCollapsed: boolean
  onToggleCollapse: () => void
}

// Icon mapping for better control
const iconMap = {
  dashboard: Home,
  form: FileText,
  post: BarChart3,
  page: Target,
  settings: Settings,
} as const

export function EnhancedSidebar({ items, isCollapsed, onToggleCollapse }: EnhancedSidebarProps) {
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const [mounted, setMounted] = useState(false)
  const [showToggle, setShowToggle] = useState(false)
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle hover-based toggle visibility for premium UX
  const handleMouseEnter = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
    setShowToggle(true)
  }

  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setShowToggle(false)
    }, 500) // 500ms delay for smooth UX
    setHoverTimeout(timeout)
  }

  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout)
      }
    }
  }, [hoverTimeout])

  if (!mounted) return null

  return (
    <TooltipProvider>
      <div
        className={cn(
          "relative flex h-full shrink-0 flex-col border-r border-border/10",
          "bg-white/98 backdrop-blur-sm",
          "sidebar-stable transition-all duration-300 ease-in-out",
          isCollapsed ? "w-[72px]" : "w-[260px]"
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Header with Logo - Clean and Minimal */}
        <div className={cn(
          "relative flex items-center border-b border-border/5 py-6",
          isCollapsed ? "justify-center px-4" : "px-6"
        )}>
          <AnimatePresence mode="wait">
            {!isCollapsed ? (
              <motion.div
                key="logo-expanded"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="flex items-center"
              >
                <Icons.logoFull className="h-8 text-foreground" />
              </motion.div>
            ) : (
              <motion.div
                key="logo-collapsed"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="flex items-center justify-center"
              >
                <Icons.logoIcon className="size-8 text-foreground" />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Hover-based Toggle Button - Premium UX */}
          <AnimatePresence>
              {showToggle && (
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                  onClick={onToggleCollapse}
                  className={cn(
                    "absolute top-[calc(50%+10px)] z-20 -translate-y-1/2",
                    "size-8 rounded-full bg-white/90 backdrop-blur-sm",
                    "border border-border/20 shadow-sm",
                    "transition-all duration-200 hover:bg-white hover:shadow-md",
                    "group flex items-center justify-center",
                    isCollapsed ? "right-[-12px]" : "right-[-12px]"
                  )}
                  aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                  title={isCollapsed ? "Expand menu" : "Collapse menu"}
                >
                  <motion.div
                    animate={{ rotate: isCollapsed ? 0 : 180 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRight className="size-4 text-muted-foreground transition-colors group-hover:text-foreground" />
                  </motion.div>
                </motion.button>
              )}
            </AnimatePresence>

        </div>

        {/* Navigation - Minimal and Clean */}
        <nav className="flex-1 space-y-2 px-3 py-8">
          <AnimatePresence>
            {items.map((item, index) => {
              const iconKey = item.icon as keyof typeof iconMap
              const Icon = iconMap[iconKey] || Home
              const isActive = pathname === item.href

              const linkContent = (
                <motion.div
                  layout
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  className={cn(
                    "group relative flex items-center gap-3 rounded-lg p-3 transition-all duration-200",
                    "hover:bg-gray-50/80",
                    isActive && "bg-gray-100/60",
                    item.disabled && "cursor-not-allowed opacity-50"
                  )}
                >
                  {/* Minimal active indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute inset-y-0 left-0 w-0.5 rounded-r-full bg-foreground/80"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}

                  <div className={cn(
                    "flex size-5 items-center justify-center transition-colors duration-200",
                    isActive ? "text-foreground" : "text-muted-foreground group-hover:text-foreground"
                  )}>
                    <Icon className="size-4" />
                  </div>

                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0, x: -8 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -8 }}
                        transition={{ duration: 0.25, ease: "easeOut" }}
                        className={cn(
                          "text-sm font-medium transition-colors duration-200",
                          isActive ? "text-foreground" : "text-muted-foreground group-hover:text-foreground"
                        )}
                      >
                        {item.title}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </motion.div>
              )

              if (isCollapsed) {
                return (
                  <Tooltip key={index} delayDuration={0}>
                    <TooltipTrigger asChild>
                      <Link 
                        href={item.disabled ? "#" : (item.href || "#")}
                        className="block"
                      >
                        {linkContent}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent 
                      side="right" 
                      sideOffset={8}
                      className="z-[100] font-medium"
                      align="center"
                    >
                      {item.title}
                    </TooltipContent>
                  </Tooltip>
                )
              }

              return (
                <Link
                  key={index}
                  href={item.disabled ? "#" : (item.href || "#")}
                  className="block"
                >
                  {linkContent}
                </Link>
              )
            })}
          </AnimatePresence>
        </nav>

        {/* Footer with User - Clean and Minimal */}
        <div className="border-t border-border/5 px-3 py-4">
          {!isCollapsed ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.button
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  className="group flex w-full items-center gap-3 rounded-lg p-3 transition-all duration-200 hover:bg-gray-50/80"
                >
                  <UserAvatar
                    user={{
                      name: user?.name || null,
                      image: null
                    }}
                    className="size-8 shadow-sm ring-1 ring-border/20"
                  />
                  <div className="min-w-0 flex-1 text-left">
                    <div className="truncate text-sm font-medium text-foreground">
                      {user?.name || 'User'}
                    </div>
                    <div className="truncate text-xs text-muted-foreground">
                      {user?.email}
                    </div>
                  </div>
                  <ChevronRight className="size-3 text-muted-foreground transition-colors group-hover:text-foreground" />
                </motion.button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start" 
                alignOffset={-8}
                sideOffset={8}
                className="z-[100] w-56"
                side="right"
              >
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user?.name}</p>
                    <p className="truncate text-sm text-muted-foreground">
                      {user?.email}
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer text-red-600 focus:text-red-600"
                  onSelect={(event) => {
                    event.preventDefault()
                    logout()
                  }}
                >
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex w-full justify-center rounded-lg p-2 transition-all duration-200 hover:bg-gray-50/80">
                      <UserAvatar
                        user={{
                          name: user?.name || null,
                          image: null
                        }}
                        className="size-7 shadow-sm ring-1 ring-border/20 transition-all duration-200 hover:ring-border/40"
                      />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent 
                    align="start" 
                    alignOffset={-8}
                    sideOffset={8}
                    className="z-[100] w-56"
                    side="right"
                  >
                    <div className="flex items-center justify-start gap-2 p-2">
                      <div className="flex flex-col space-y-1 leading-none">
                        <p className="font-medium">{user?.name}</p>
                        <p className="truncate text-sm text-muted-foreground">
                          {user?.email}
                        </p>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard">Dashboard</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/settings">Settings</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-red-600 focus:text-red-600"
                      onSelect={(event) => {
                        event.preventDefault()
                        logout()
                      }}
                    >
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent 
                side="right" 
                sideOffset={8}
                className="z-[100] font-medium"
                align="center"
              >
                {user?.name}
              </TooltipContent>
            </Tooltip>
          )}

          {/* Minimal Footer Branding */}
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="mt-6 px-3"
              >
                <p className="text-center text-xs font-medium text-muted-foreground/60">
                  Powered by TractionX
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </TooltipProvider>
  )
}
