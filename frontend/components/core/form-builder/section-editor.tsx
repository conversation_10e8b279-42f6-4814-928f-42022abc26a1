"use client"

import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Trash2 } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Form as FormUI, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from '@/components/ui/use-toast';
import { Section, SectionCreateRequest } from '@/lib/types/form';
import FormAPI from '@/lib/api/form-api';

// Section validation schema
const sectionSchema = z.object({
  title: z.string().min(1, 'Section title is required'),
  description: z.string(),
  repeatable: z.boolean().default(false),
});

type SectionFormValues = z.infer<typeof sectionSchema>;

interface SectionEditorProps {
  section?: Partial<Section>;
  index: number;
  onUpdate: (index: number, section: Partial<Section>) => void;
  onDelete: (index: number) => void;
  onSave?: () => void;
}

export function SectionEditor({ section, index, onUpdate, onDelete, onSave }: SectionEditorProps) {
  const [isSaving, setIsSaving] = React.useState(false);

  const form = useForm<SectionFormValues>({
    resolver: zodResolver(sectionSchema),
    defaultValues: {
      title: section?.title || '',
      description: section?.description || '',
      repeatable: section?.repeatable || false,
    },
  });

  // Update parent component when form values change
  const onSubmit = async (values: SectionFormValues) => {
    try {
      setIsSaving(true);
      console.log('Section form submitted with values:', values);

      const updatedSection = {
        ...section,
        title: values.title,
        description: values.description,
        repeatable: values.repeatable,
        // Ensure order is preserved
        order: section?.order ?? index,
      };

      console.log('Updating section with:', updatedSection);

      // Check if we have a section ID (for existing sections)
      const sectionId = section?._id || section?.id;

      // First update the local state via the onUpdate function
      await onUpdate(index, updatedSection);

      // If we have a section ID, also directly call the API to ensure it's saved
      if (sectionId) {
        console.log(`Section has ID ${sectionId}, directly calling API to update section`);

        // Prepare the section data for the API
        const sectionRequest: SectionCreateRequest = {
          title: values.title,
          description: values.description,
          order: updatedSection.order,
          repeatable: values.repeatable,
        };

        try {
          // Call the API directly to ensure the section is saved
          const updatedSectionFromApi = await FormAPI.updateSection(sectionId, sectionRequest);
          console.log('Section updated successfully via direct API call:', updatedSectionFromApi);
        } catch (apiError) {
          console.error('Error updating section via direct API call:', apiError);
          throw apiError;
        }
      } else {
        console.log('Section has no ID, skipping direct API call');
      }

      // Call onSave callback if provided
      if (onSave) {
        await onSave();
      }

      // Show a toast notification to confirm the save
      toast({
        title: "Section saved",
        description: "Your section has been updated successfully.",
      });
    } catch (error) {
      console.error('Error saving section:', error);
      toast({
        title: "Error saving section",
        description: "There was an error saving your section. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Manual save instead of auto-save to prevent infinite loops
  const saveChanges = React.useCallback(() => {
    console.log('Saving section changes...');
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  // No auto-save - we'll use manual save buttons instead

  return (
    <Card className="mb-6 w-full">
      <CardHeader className="flex flex-row items-start justify-between space-y-0">
        <div>
          <CardTitle>Section {index + 1}</CardTitle>
          <CardDescription>
            Define a section of your form
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('Header save button clicked');
              form.handleSubmit(onSubmit)();
            }}
            type="button"
            className="h-8"
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onDelete(index);
            }}
            type="button"
            className="text-destructive hover:bg-destructive/10 hover:text-destructive/90"
          >
            <Trash2 className="size-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <FormUI {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Section Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter section title" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive title for this section
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter section description"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Explain what information should be collected in this section
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="repeatable"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Repeatable Section</FormLabel>
                      <FormDescription>
                        Allow users to add multiple instances of this section
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="mt-6">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving Section...' : 'Save Section'}
                </Button>
              </div>
            </div>
          </form>
        </FormUI>
      </CardContent>
    </Card>
  );
}
