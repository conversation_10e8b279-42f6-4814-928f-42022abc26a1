"use client"

import React, { useState } from 'react';
import { X, FileText, Layers, HelpCircle, Eye, Settings, Save, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { FormWithDetails, Section, Question, QuestionType, QUESTION_TYPES, isCoreFieldQuestion, CoreFieldType } from '@/lib/types/form';
import { VisibilityEditor } from './visibility-editor';
import { QuestionConfig } from './question-config';
import { ExclusionFilterBlock } from './exclusion-filter';
import { FormSharingPanel } from './form-sharing-panel';

interface FormDetailPanelProps {
  form: FormWithDetails;
  selectedItem: { type: 'form' | 'section' | 'question'; id: string } | null;
  onUpdateForm: (updates: any) => void;
  onUpdateSection: (sectionId: string, updates: any) => void;
  onUpdateQuestion: (questionId: string, updates: any) => void;
  onClose: () => void;
}

export function FormDetailPanel({
  form,
  selectedItem,
  onUpdateForm,
  onUpdateSection,
  onUpdateQuestion,
  onClose
}: FormDetailPanelProps) {
  const [localChanges, setLocalChanges] = useState<any>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Get the selected item data
  const getSelectedData = () => {
    if (!selectedItem) return null;

    switch (selectedItem.type) {
      case 'form':
        return form;
      case 'section':
        return form.sections.find(s => (s._id || s.id) === selectedItem.id);
      case 'question':
        for (const section of form.sections) {
          const question = section.questions.find(q => (q._id || q.id) === selectedItem.id);
          if (question) return question;
        }
        return null;
      default:
        return null;
    }
  };

  const selectedData = getSelectedData();

  // Validate core fields are present
  const validateCoreFields = () => {
    const allQuestions = form.sections.flatMap(section => section.questions);
    const coreFields = {
      [CoreFieldType.COMPANY_NAME]: false,
      [CoreFieldType.STAGE]: false,
      [CoreFieldType.SECTOR]: false,
    };

    allQuestions.forEach(question => {
      if (question.core_field) {
        coreFields[question.core_field] = true;
      }
    });

    return {
      isValid: Object.values(coreFields).every(Boolean),
      missing: Object.entries(coreFields)
        .filter(([_, present]) => !present)
        .map(([field, _]) => field)
    };
  };

  const coreFieldValidation = validateCoreFields();

  const handleLocalChange = (field: string, value: any) => {
    // Prevent activating form without core fields
    if (field === 'is_active' && value === true && !coreFieldValidation.isValid) {
      alert(`Cannot activate form. Missing core fields: ${coreFieldValidation.missing.join(', ')}`);
      return;
    }
    
    setLocalChanges(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  const handleSave = () => {
    if (!selectedItem || !hasUnsavedChanges) return;

    switch (selectedItem.type) {
      case 'form':
        onUpdateForm(localChanges);
        break;
      case 'section':
        onUpdateSection(selectedItem.id, localChanges);
        break;
      case 'question':
        onUpdateQuestion(selectedItem.id, localChanges);
        break;
    }

    setLocalChanges({});
    setHasUnsavedChanges(false);
  };

  const renderFormDetails = () => (
    <div className="space-y-6">
      {/* Basic Form Settings Card */}
      <Card className="rounded-xl border border-gray-200 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900">
            <FileText className="size-5 text-gray-700" />
            Basic Information
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-gray-600">
            Configure basic form information and settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <Label htmlFor="form-name" className="text-sm font-medium">Form Name</Label>
            <Input
              id="form-name"
              value={localChanges.name ?? form.name}
              onChange={(e) => handleLocalChange('name', e.target.value)}
              placeholder="Enter form name"
              className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="form-description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="form-description"
              value={localChanges.description ?? form.description}
              onChange={(e) => handleLocalChange('description', e.target.value)}
              placeholder="Describe what this form is for"
              className="mb-4 mt-2 min-h-[120px] resize-none leading-relaxed"
            />
          </div>

          {!coreFieldValidation.isValid && (
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
              <div className="mb-2 flex items-center gap-2">
                <AlertCircle className="size-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-900">
                  Missing Core Fields
                </span>
              </div>
              <p className="mb-2 text-xs text-amber-700">
                This form is missing required core dashboard fields: {coreFieldValidation.missing.join(', ')}
              </p>
              <p className="text-xs text-amber-600">
                Core fields are automatically created in new forms. If missing, please create a new form or contact support.
              </p>
            </div>
          )}

          <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-6">
            <div className="space-y-1">
              <Label className="text-sm font-medium text-gray-900">Active Status</Label>
              <p className="text-xs leading-relaxed text-gray-600">
                Active forms can receive submissions
                {!coreFieldValidation.isValid && (
                  <span className="mt-1 block text-amber-600">
                    Cannot activate without core fields
                  </span>
                )}
              </p>
            </div>
            <Switch
              checked={localChanges.is_active ?? form.is_active}
              onCheckedChange={(checked) => handleLocalChange('is_active', checked)}
              disabled={!coreFieldValidation.isValid && (localChanges.is_active ?? form.is_active) === false}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Statistics Card */}
      <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold">
            <Settings className="size-5 text-primary" />
            Form Statistics
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-muted-foreground">
            Overview of form structure and metadata
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6 text-sm">
            <div className="space-y-2 rounded-lg bg-muted/30 p-4">
              <span className="text-xs uppercase tracking-wide text-muted-foreground">Sections</span>
              <p className="text-lg font-semibold">{form.sections.length}</p>
            </div>
            <div className="space-y-2 rounded-lg bg-muted/30 p-4">
              <span className="text-xs uppercase tracking-wide text-muted-foreground">Questions</span>
              <p className="text-lg font-semibold">
                {form.sections.reduce((total, section) => total + section.questions.length, 0)}
              </p>
            </div>
            <div className="space-y-2 rounded-lg bg-muted/30 p-4">
              <span className="text-xs uppercase tracking-wide text-muted-foreground">Version</span>
              <p className="text-lg font-semibold">{form.version}</p>
            </div>
            <div className="space-y-2 rounded-lg bg-muted/30 p-4">
              <span className="text-xs uppercase tracking-wide text-muted-foreground">Created</span>
              <p className="text-lg font-semibold">
                {form.created_at ? new Date(form.created_at * 1000).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exclusion Filters Card */}
      <Card className="rounded-xl border border-gray-200 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900">
            <Eye className="size-5 text-gray-700" />
            Exclusion Filters
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-gray-600">
            Configure filters to exclude certain submissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExclusionFilterBlock form={form} />
        </CardContent>
      </Card>

      {/* Form Sharing Card */}
      <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold">
            <Settings className="size-5 text-primary" />
            Form Sharing
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-muted-foreground">
            Create public links to share this form with external users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormSharingPanel
            formId={form._id || form.id!}
            formName={form.name}
          />
        </CardContent>
      </Card>
    </div>
  );

  const renderSectionDetails = () => {
    const section = selectedData as Section;
    if (!section) return null;

    return (
      <div className="space-y-6">
        {/* Basic Section Settings Card */}
        <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold">
              <Layers className="size-5 text-primary" />
              Section Information
            </CardTitle>
            <CardDescription className="text-sm leading-relaxed text-muted-foreground">
              Configure section title, description, and behavior
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="section-title" className="text-sm font-medium">Section Title</Label>
              <Input
                id="section-title"
                value={localChanges.title ?? section.title}
                onChange={(e) => handleLocalChange('title', e.target.value)}
                placeholder="Enter section title"
                className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="section-description" className="text-sm font-medium">Description</Label>
              <Textarea
                id="section-description"
                value={localChanges.description ?? section.description}
                onChange={(e) => handleLocalChange('description', e.target.value)}
                placeholder="Describe this section (optional)"
                className="mb-4 mt-2 min-h-[100px] resize-none leading-relaxed"
              />
            </div>

            <div className="flex items-center justify-between rounded-lg border bg-muted/30 p-6">
              <div className="space-y-1">
                <Label className="text-sm font-medium">Repeatable Section</Label>
                <p className="text-xs leading-relaxed text-muted-foreground">
                  Allow users to add multiple instances
                </p>
              </div>
              <Switch
                checked={localChanges.repeatable ?? section.repeatable}
                onCheckedChange={(checked) => handleLocalChange('repeatable', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Section Statistics Card */}
        <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold">
              <Settings className="size-5 text-primary" />
              Section Statistics
            </CardTitle>
            <CardDescription className="text-sm leading-relaxed text-muted-foreground">
              Overview of section structure and position
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6 text-sm">
              <div className="space-y-2 rounded-lg bg-muted/30 p-4">
                <span className="text-xs uppercase tracking-wide text-muted-foreground">Questions</span>
                <p className="text-lg font-semibold">{section.questions.length}</p>
              </div>
              <div className="space-y-2 rounded-lg bg-muted/30 p-4">
                <span className="text-xs uppercase tracking-wide text-muted-foreground">Position</span>
                <p className="text-lg font-semibold">{section.order + 1}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderQuestionDetails = () => {
    const question = selectedData as Question;
    if (!question) return null;

    // Merge local changes with the original question
    const questionWithChanges = { ...question, ...localChanges };

    return (
      <div className="space-y-6">
        {/* Question Configuration */}
        <QuestionConfig
          question={questionWithChanges}
          onUpdate={(updates) => {
            // Handle multiple field updates
            Object.entries(updates).forEach(([key, value]) => {
              handleLocalChange(key, value);
            });
          }}
        />

        {/* Visibility Conditions Card */}
        <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold">
              <Eye className="size-5 text-primary" />
              Visibility Conditions
            </CardTitle>
            <CardDescription className="text-sm leading-relaxed text-muted-foreground">
              Configure when this question should be visible to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VisibilityEditor
              question={questionWithChanges}
              form={form}
              onUpdate={(visibilityCondition) => handleLocalChange('visibility_condition', visibilityCondition)}
            />
          </CardContent>
        </Card>

        {/* Question Details Card */}
        <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold">
              <HelpCircle className="size-5 text-primary" />
              Question Details
            </CardTitle>
            <CardDescription className="text-sm leading-relaxed text-muted-foreground">
              Overview of question metadata and structure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 text-sm">
              <div className="space-y-2 rounded-lg bg-muted/30 p-4">
                <span className="text-xs uppercase tracking-wide text-muted-foreground">Position</span>
                <p className="text-lg font-semibold">{question.order + 1}</p>
              </div>
              {question.options && (
                <div className="space-y-2 rounded-lg bg-muted/30 p-4">
                  <span className="text-xs uppercase tracking-wide text-muted-foreground">Options</span>
                  <p className="text-lg font-semibold">{question.options.length} option{question.options.length !== 1 ? 's' : ''}</p>
                </div>
              )}
              <div className="space-y-2 rounded-lg bg-muted/30 p-4">
                <span className="text-xs uppercase tracking-wide text-muted-foreground">Created</span>
                <p className="text-lg font-semibold">
                  {question.created_at ? new Date(question.created_at * 1000).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderContent = () => {
    if (!selectedItem || !selectedData) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-1 items-center justify-center p-6 text-center"
        >
          <div>
            <FileText className="mx-auto mb-6 size-12 text-muted-foreground/50" />
            <h3 className="mb-3 text-lg font-semibold">No Selection</h3>
            <p className="max-w-sm text-sm leading-relaxed text-muted-foreground">
              Select a form element from the left panel to view and edit its properties here.
            </p>
          </div>
        </motion.div>
      );
    }

    const getIcon = () => {
      switch (selectedItem.type) {
        case 'form': return FileText;
        case 'section': return Layers;
        case 'question': return HelpCircle;
        default: return FileText;
      }
    };

    const Icon = getIcon();
    const title = selectedItem.type === 'form' ? 'Form Settings' :
                  selectedItem.type === 'section' ? 'Section Settings' : 'Question Settings';

    return (
      <>
        <div className="border-b p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Icon className="size-5 text-primary" />
              <h3 className="text-base font-semibold sm:text-lg">{title}</h3>
            </div>
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <Button size="sm" onClick={handleSave} className="h-10">
                  <Save className="mr-2 size-4" />
                  Save
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={onClose} className="h-10 lg:hidden">
                <X className="size-4" />
              </Button>
            </div>
          </div>
        </div>

        <ScrollArea className="flex-1 px-4 sm:px-6">
          <div className="mx-auto max-w-full py-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedItem.type + selectedItem.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                {selectedItem.type === 'form' && renderFormDetails()}
                {selectedItem.type === 'section' && renderSectionDetails()}
                {selectedItem.type === 'question' && renderQuestionDetails()}
              </motion.div>
            </AnimatePresence>
          </div>
        </ScrollArea>
      </>
    );
  };

  return (
    <div className="flex h-full flex-col">
      {renderContent()}
    </div>
  );
}
