"use client"

import React, { useState, useRef } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import * as z from 'zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogBody,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-react';

import { FormWithDetails, QuestionType, QUESTION_TYPES } from '@/lib/types/form';

const questionSchema = z.object({
  type: z.nativeEnum(QuestionType),
  label: z.string().min(1, 'Question label is required').max(200, 'Label must be less than 200 characters'),
  help_text: z.string().max(500, 'Help text must be less than 500 characters').nullable().optional().transform(val => val || undefined),
  required: z.boolean(),
  options: z.array(z.object({
    label: z.string().min(1, 'Option label is required'),
    value: z.string().min(1, 'Option value is required'),
  })).optional(),
});

type QuestionData = z.infer<typeof questionSchema>;

interface AddQuestionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAdd: (data: QuestionData) => Promise<void>;
  form: FormWithDetails;
  sectionId: string;
}

export function AddQuestionDialog({
  open,
  onOpenChange,
  onAdd,
  form: formData,
  sectionId
}: AddQuestionDialogProps) {
  const [selectedType, setSelectedType] = useState<QuestionType | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  const form = useForm<QuestionData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      type: QuestionType.SHORT_TEXT,
      label: '',
      help_text: '',
      required: false,
      options: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'options',
  });

  const watchedType = form.watch('type');
  const typeInfo = QUESTION_TYPES.find(t => t.type === watchedType);
  const hasOptions = typeInfo?.hasOptions || false;

  const handleSubmit = async (data: QuestionData) => {
    console.log('🚀 FORM SUBMIT TRIGGERED!', data);
    
    try {
      // Transform empty strings to undefined for optional fields
      const cleanedData = {
        ...data,
        help_text: data.help_text?.trim() || undefined,
      };
      
      // Remove options if question type doesn't support them
      if (!hasOptions) {
        cleanedData.options = undefined;
      }
      
      console.log('🚀 Calling onAdd with cleaned data:', cleanedData);
      await onAdd(cleanedData);
      
      console.log('🚀 onAdd completed successfully');
      form.reset();
      setSelectedType(null);
      onOpenChange(false);
    } catch (error) {
      console.error('❌ Error adding question:', error);
    }
  };

  const handleCancel = () => {
    form.reset();
    setSelectedType(null);
    onOpenChange(false);
  };

  const handleAddQuestion = () => {
    formRef.current?.requestSubmit();
  };

  const addOption = () => {
    const currentOptions = form.getValues('options') || [];
    const optionNumber = currentOptions.length + 1;
    const label = `Option ${optionNumber}`;
    append({ 
      label: label, 
      value: label.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_') 
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent size="large">
        <DialogHeader>
          <DialogTitle>Add New Question</DialogTitle>
          <DialogDescription>
            Create a new question for this section.
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          <Form {...form}>
            <form ref={formRef} onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 px-2">
              {/* Question Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-1 text-sm font-medium md:text-base">Question Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12 rounded-xl border-gray-200 bg-background px-4">
                          <SelectValue placeholder="Select question type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-background">
                        {QUESTION_TYPES.map((type) => (
                          <SelectItem key={type.type} value={type.type}>
                            <div className="flex flex-col items-start text-left">
                              <span className="font-medium">{type.label}</span>
                              {/* <span className="text-xs text-muted-foreground">{type.description}</span> */}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of input for this question.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Question Label */}
              <FormField
                control={form.control}
                name="label"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-1 text-sm font-medium md:text-base">Question Label</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter question label..." {...field} className="h-12 rounded-xl border-gray-200 px-4" />
                    </FormControl>
                    <FormDescription>
                      The main question text that users will see.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Help Text */}
              <FormField
                control={form.control}
                name="help_text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-1 text-sm font-medium md:text-base">Help Text (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter help text..."
                        className="min-h-[60px] rounded-xl border-gray-200 px-4 py-3"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>
                      Additional instructions or context for this question.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Required */}
              <FormField
                control={form.control}
                name="required"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-xl border border-gray-200 p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base font-medium">Required Question</FormLabel>
                      <FormDescription>
                        Users must answer this question to submit the form.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Options (for select types) */}
              {hasOptions && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-sm font-medium md:text-base">Options</FormLabel>
                    <Button type="button" variant="outline" size="sm" onClick={addOption} className="h-10 rounded-xl px-4">
                      <Plus className="mr-2 size-4" />
                      Add Option
                    </Button>
                  </div>
                  
                  {fields.length === 0 && (
                    <Card className="border-dashed border-muted bg-muted">
                      <CardContent className="flex items-center justify-center bg-muted py-8">
                        <div className="text-center">
                          <p className="mb-2 text-muted-foreground">No options added yet</p>
                          <Button type="button" variant="outline" size="sm" onClick={addOption} className="h-10 rounded-xl px-4">
                            <Plus className="mr-2 size-4" />
                            Add First Option
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {fields.map((field, index) => (
                    <Card key={field.id} className="border border-muted bg-background">
                      <CardContent className="pt-4">
                        <div className="flex items-end gap-3">
                          <div className="flex-1">
                            <FormField
                              control={form.control}
                              name={`options.${index}.label`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="mb-1 text-sm font-medium">Label</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Option label..." 
                                      {...field}
                                      className="h-12 rounded-xl border-gray-200 px-4"
                                      onChange={(e) => {
                                        const label = e.target.value;
                                        const autoValue = label.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_');
                                        field.onChange(label);
                                        form.setValue(`options.${index}.value`, autoValue);
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => remove(index)}
                            className="h-10 rounded-xl px-4 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="size-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4">
                <Button type="button" variant="outline" onClick={handleCancel} className="h-12 rounded-xl px-6 font-semibold">
                  Cancel
                </Button>
                <Button 
                  type="button" 
                  className="h-12 rounded-xl bg-primary px-6 font-semibold text-primary-foreground transition-colors duration-100 hover:bg-primary/90"
                  onClick={async () => {
                    console.log('🔘 Submit button clicked!');
                    
                    // Trigger validation manually first
                    const isValid = await form.trigger();
                    console.log('🔘 Form validation result:', isValid);
                    console.log('🔘 Form errors:', form.formState.errors);
                    
                    if (isValid) {
                      const formData = form.getValues();
                      console.log('🔘 Form data:', formData);
                      await handleSubmit(formData);
                    }
                  }}
                >
                  Add Question
                </Button>
              </div>
            </form>
          </Form>
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
