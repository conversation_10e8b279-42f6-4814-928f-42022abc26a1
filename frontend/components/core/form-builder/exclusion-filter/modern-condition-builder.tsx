"use client"

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Trash2, X, ChevronDown, Check } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { Question } from '@/lib/types/form';
import { ExclusionOperator } from '@/lib/types/exclusion-filter';

interface LocalCondition {
  id: string;
  question_id: string;
  operator: ExclusionOperator;
  value: any;
  isValid: boolean;
  error?: string;
}

interface ModernConditionBuilderProps {
  condition: LocalCondition;
  questions: Question[];
  index: number;
  showLogicOperator?: boolean;
  onUpdate: (updates: Partial<LocalCondition>) => void;
  onRemove: () => void;
}

// Updated operator mappings per PRD requirements
const OPERATORS_BY_TYPE = {
  single_select: [
    { value: ExclusionOperator.EQUALS, label: 'Equals' },
    { value: ExclusionOperator.NOT_EQUALS, label: 'Not Equals' },
    { value: ExclusionOperator.IN, label: 'In' },
    { value: ExclusionOperator.NOT_IN, label: 'Not In' }
  ],
  multi_select: [
    { value: ExclusionOperator.CONTAINS, label: 'Contains Any' },
    { value: ExclusionOperator.NOT_CONTAINS, label: 'Not Contains Any' }
  ],
  boolean: [
    { value: ExclusionOperator.EQUALS, label: 'Equals' }
  ],
  number: [
    { value: ExclusionOperator.EQUALS, label: 'Equals' },
    { value: ExclusionOperator.NOT_EQUALS, label: 'Not Equals' },
    { value: ExclusionOperator.GREATER_THAN, label: 'Greater Than' },
    { value: ExclusionOperator.LESS_THAN, label: 'Less Than' },
    { value: ExclusionOperator.GREATER_THAN_OR_EQUAL, label: 'Greater Than or Equal' },
    { value: ExclusionOperator.LESS_THAN_OR_EQUAL, label: 'Less Than or Equal' }
  ]
};

// Notion-style Tags Picker Component
interface TagsPickerProps {
  question: Question;
  selectedValues: string[];
  onValuesChange: (values: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  operator?: ExclusionOperator;
}

function TagsPicker({ question, selectedValues, onValuesChange, placeholder = "Select options...", disabled = false, operator }: TagsPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isMounted, setIsMounted] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle boolean questions specially - convert true/false to Yes/No for display
  const displayOptions = question.options?.map(option => ({
    ...option,
    label: option.value === 'true' ? 'Yes' : option.value === 'false' ? 'No' : option.label
  })) || [];

  // Convert display values back to boolean for storage
  const handleValueChange = (values: string[]) => {
    const convertedValues = values.map(value => {
      if (value === 'Yes') return 'true';
      if (value === 'No') return 'false';
      return value;
    });
    onValuesChange(convertedValues);
  };

  // Convert stored values to display values
  const displayValues = selectedValues.map(value => {
    if (value === 'true') return 'Yes';
    if (value === 'false') return 'No';
    return value;
  });

  // Filter options based on search term
  const filteredOptions = useMemo(() => {
    if (!searchTerm) return displayOptions;
    return displayOptions.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [displayOptions, searchTerm]);

  const availableOptions = question.options || [];

  // Ensure component is mounted (client-side)
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Calculate dropdown position with smart upward/downward flipping
  const updatePosition = () => {
    if (!triggerRef.current) return;

    const rect = triggerRef.current.getBoundingClientRect();
    const scrollY = window.pageYOffset || window.scrollY || 0;
    const scrollX = window.pageXOffset || window.scrollX || 0;
    const dropdownHeight = 320;
    const spaceBelow = window.innerHeight - rect.bottom;
    const spaceAbove = rect.top;

    let top = rect.bottom + scrollY + 4;
    let direction: 'down' | 'up' = 'down';

    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
      top = rect.top + scrollY - dropdownHeight - 4;
      direction = 'up';
    }

    // Calculate final position with viewport bounds checking
    let finalTop = Math.max(10, isNaN(top) ? 100 : top);
    let finalLeft = Math.max(10, isNaN(rect.left + scrollX) ? 100 : rect.left + scrollX);
    let finalWidth = Math.max(200, isNaN(rect.width) ? 300 : rect.width);

    // Ensure dropdown stays within viewport bounds
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const dropdownWidth = Math.min(finalWidth, 400); // Max 400px wide
    const maxDropdownHeight = 320;

    // Horizontal bounds check
    if (finalLeft + dropdownWidth > viewportWidth - 20) {
      finalLeft = Math.max(20, viewportWidth - dropdownWidth - 20);
    }
    if (finalLeft < 20) {
      finalLeft = 20;
    }

    // Vertical bounds check
    if (finalTop + maxDropdownHeight > viewportHeight - 20) {
      finalTop = Math.max(20, viewportHeight - maxDropdownHeight - 20);
    }
    if (finalTop < 20) {
      finalTop = 20;
    }

    // If still off-screen, center it
    if (finalLeft > viewportWidth - 100 || finalTop > viewportHeight - 100) {
      finalLeft = Math.max(20, (viewportWidth - dropdownWidth) / 2);
      finalTop = Math.max(20, (viewportHeight - maxDropdownHeight) / 2);
    }

    const finalPosition = {
      top: finalTop,
      left: finalLeft,
      width: dropdownWidth,
      direction,
    };

    setPosition(finalPosition);
  };

  // Update position when opening
  useEffect(() => {
    if (isOpen && isMounted) {
      updatePosition();
      
      // Update position on scroll/resize
      const handleUpdate = () => updatePosition();
      window.addEventListener('scroll', handleUpdate, true);
      window.addEventListener('resize', handleUpdate);
      
      return () => {
        window.removeEventListener('scroll', handleUpdate, true);
        window.removeEventListener('resize', handleUpdate);
      };
    }
  }, [isOpen, isMounted]);

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        triggerRef.current && 
        !triggerRef.current.contains(event.target as Node) &&
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    function handleEscapeKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isOpen]);

  const toggleOption = (value: string) => {
    // Determine if multiple selection is allowed based on question type and operator
    const allowMultiple = question.type === 'multi_select' || 
                          (question.type === 'single_select' && 
                           (operator === ExclusionOperator.IN || operator === ExclusionOperator.NOT_IN));
    
    const displayValue = value === 'true' ? 'Yes' : value === 'false' ? 'No' : value;
    
    if (allowMultiple) {
      const newValues = displayValues.includes(displayValue)
        ? displayValues.filter(v => v !== displayValue)
        : [...displayValues, displayValue];
      handleValueChange(newValues);
    } else {
      // Single selection mode for boolean and single-select with EQUALS/NOT_EQUALS
      handleValueChange(displayValues.includes(displayValue) ? [] : [value]);
    }
  };

  const removeValue = (value: string) => {
    const newValues = displayValues.filter(v => v !== value);
    handleValueChange(newValues);
  };

  const getOptionLabel = (value: string) => {
    const option = displayOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <>
      {/* Selected Tags Display */}
      <div
        ref={triggerRef}
        className={cn(
          "min-h-[32px] w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background",
          "flex cursor-pointer flex-wrap items-center gap-1",
          disabled && "cursor-not-allowed opacity-50",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            const newIsOpen = !isOpen;
            setIsOpen(newIsOpen);
            
            // Immediately update position when opening
            if (newIsOpen) {
              setTimeout(updatePosition, 0);
            }
          }
        }}
      >
        {displayValues.length > 0 ? (
          displayValues.map((value) => (
            <Badge
              key={value}
              variant="secondary"
              className="h-6 gap-1 px-2 text-xs"
            >
              {getOptionLabel(value)}
              {!disabled && (
                <X
                  className="size-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeValue(value);
                  }}
                />
              )}
            </Badge>
          ))
        ) : (
          <span className="text-muted-foreground">{placeholder}</span>
        )}
        <ChevronDown className={cn("ml-auto size-4 transition-transform", isOpen && "rotate-180")} />
      </div>

      {/* Portal Dropdown */}
      {isMounted && isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[99999] max-h-80 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg"
          style={{
            top: position.top,
            left: position.left,
            width: position.width,
            minWidth: '200px',
            boxShadow: "0 4px 24px rgba(0,0,0,0.16)"
          }}
        >
          {/* Search Input */}
          <div className="border-b p-2">
            <Input
              ref={inputRef}
              placeholder="Search options..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-8"
              autoFocus
            />
          </div>

          {/* Options List */}
          <div className="max-h-40 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => {
                const isSelected = displayValues.includes(option.label);
                return (
                  <div
                    key={option.value}
                    className={cn(
                      "cursor-pointer px-3 py-2 text-sm transition-colors hover:bg-accent",
                      "flex items-center justify-between",
                      isSelected && "bg-accent text-accent-foreground"
                    )}
                    onClick={() => {
                      toggleOption(option.value);
                    }}
                  >
                    <span className="flex-1 truncate">{option.label}</span>
                    {isSelected && (
                      <Check className="ml-2 size-4" />
                    )}
                  </div>
                );
              })
            ) : (
              <div className="px-3 py-2 text-sm text-muted-foreground">
                No options found
              </div>
            )}
          </div>
        </div>,
        document.body
      )}
    </>
  );
}

// Value Input Component for different question types
interface ValueInputProps {
  question: Question;
  operator: ExclusionOperator;
  value: any;
  onValueChange: (value: any) => void;
  placeholder?: string;
  disabled?: boolean;
}

function ValueInput({ question, operator, value, onValueChange, placeholder = "Enter value...", disabled = false }: ValueInputProps) {
  switch (question.type) {
    case 'number':
      return (
        <Input
          type="number"
          value={value ?? ''}
          onChange={(e) => onValueChange(e.target.value ? Number(e.target.value) : '')}
          placeholder={placeholder}
          disabled={disabled}
          className="h-9 rounded-md border border-border px-3 text-base focus:ring-2 focus:ring-primary"
        />
      );
    case 'boolean':
      return (
        <Select
          value={value === true ? 'true' : value === false ? 'false' : ''}
          onValueChange={(v) => onValueChange(v === 'true')}
          disabled={disabled}
        >
          <SelectTrigger className="h-9 w-full rounded-md border border-border px-3 text-base focus:ring-2 focus:ring-primary">
            <SelectValue placeholder="Select Yes or No" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">Yes</SelectItem>
            <SelectItem value="false">No</SelectItem>
          </SelectContent>
        </Select>
      );
    case 'single_select':
    case 'multi_select': {
      const isMultiValue =
        question.type === 'multi_select' ||
        operator === ExclusionOperator.IN ||
        operator === ExclusionOperator.NOT_IN;

      return (
        <TagsPicker
          question={question}
          selectedValues={Array.isArray(value) ? value : (value ? [value] : [])}
          onValuesChange={(values: string[]) => {
            if (isMultiValue) {
              onValueChange(values);
            } else {
              onValueChange(values.length > 0 ? values[0] : null);
            }
          }}
          placeholder={placeholder}
          disabled={disabled}
          operator={operator}
        />
      );
    }
    default:
      return (
        <Input
          value={value || ''}
          onChange={(e) => onValueChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="h-9 rounded-md border border-border px-3 text-base focus:ring-2 focus:ring-primary"
        />
      );
  }
}

export function ModernConditionBuilder({
  condition,
  questions,
  index,
  showLogicOperator = false,
  onUpdate,
  onRemove,
}: ModernConditionBuilderProps) {
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);

  // Find the selected question
  useEffect(() => {
    if (condition.question_id) {
      const question = questions.find(q => 
        (q._id === condition.question_id) || (q.id === condition.question_id)
      );
      setSelectedQuestion(question || null);
    } else {
      setSelectedQuestion(null);
    }
  }, [condition.question_id, questions]);

  // Validate the condition
  useEffect(() => {
    let isValid = true;
    let error = '';

    if (!condition.question_id) {
      isValid = false;
      error = 'Please select a question';
    } else if (!selectedQuestion) {
      isValid = false;
      error = 'Selected question not found';
    } else if (!condition.operator) {
      isValid = false;
      error = 'Please select an operator';
    } else if (condition.value === '' || condition.value === null || condition.value === undefined) {
      isValid = false;
      error = 'Please select a value';
    } else {
      // Validate value based on question type and operator
      if (selectedQuestion.type === 'multi_select') {
        if (!Array.isArray(condition.value) || condition.value.length === 0) {
          isValid = false;
          error = 'Please select at least one option';
        }
      } else if (selectedQuestion.type === 'number') {
        if (condition.value === '' || condition.value === null || condition.value === undefined) {
          isValid = false;
          error = 'Please enter a numeric value';
        }
      }
    }

    // Only update if validation state has actually changed
    if (condition.isValid !== isValid || condition.error !== error) {
      onUpdate({ isValid, error });
    }
  }, [condition, selectedQuestion, onUpdate]);

  // Get available operators for the selected question type
  const getAvailableOperators = () => {
    if (!selectedQuestion) return [];
    return OPERATORS_BY_TYPE[selectedQuestion.type as keyof typeof OPERATORS_BY_TYPE] || [];
  };

  // Handle question selection
  const handleQuestionChange = (questionId: string) => {
    const question = questions.find(q => (q._id === questionId) || (q.id === questionId));
    
    onUpdate({
      question_id: questionId,
      operator: ExclusionOperator.EQUALS, // Reset operator
      value: '', // Reset value
      isValid: false
    });
  };

  // Handle operator change
  const handleOperatorChange = (operator: ExclusionOperator) => {
    // Reset value when operator changes
    let resetValue: any = '';
    
    const isMultiValue =
      selectedQuestion?.type === 'multi_select' ||
      (selectedQuestion?.type === 'single_select' &&
        (operator === ExclusionOperator.IN || operator === ExclusionOperator.NOT_IN));

    if (isMultiValue) {
      resetValue = [];
    }

    onUpdate({
      operator,
      value: resetValue,
      isValid: false
    });
  };

  const availableOperators = getAvailableOperators();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="relative flex items-center gap-x-6 overflow-visible rounded-xl border border-border/60 bg-background p-4 shadow-sm">
        {/* Colored left accent */}
        <div className="absolute inset-y-0 left-0 w-1 rounded-l-xl bg-red-500" />

        {/* Logic Operator Badge */}
        {showLogicOperator && (
          <div className="mr-2 flex min-w-[80px] flex-col items-center justify-center">
            <Badge variant="outline" className="rounded-full bg-muted px-3 py-1 text-xs">
              AND
            </Badge>
          </div>
        )}

        {/* Badge for non-logic conditions */}
        {!showLogicOperator && (
          <div className="mr-2 flex min-w-[80px] flex-col items-center justify-center">
            <Badge variant="secondary" className="rounded-full bg-muted px-3 py-1 text-xs">
              Exclude
            </Badge>
          </div>
        )}

        {/* Inputs: Question / Operator / Value */}
        <div className="grid flex-1 grid-cols-1 items-end gap-4 lg:grid-cols-3">
          {/* Question Selection */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Question</Label>
            <Select 
              value={condition.question_id} 
              onValueChange={handleQuestionChange}
            >
              <SelectTrigger className="h-9 rounded-md border border-border bg-background px-3 text-base focus:ring-2 focus:ring-primary">
                <SelectValue placeholder="Select question..." />
              </SelectTrigger>
              <SelectContent className="rounded-md py-1">
                {questions.map((question) => {
                  const questionId = question._id || question.id;
                  if (!questionId) return null;
                  
                  return (
                    <SelectItem key={questionId} value={questionId} className="px-3 py-2">
                      <div className="flex w-full flex-col items-start gap-1">
                        <span className="max-w-[220px] truncate text-base font-medium sm:max-w-[320px]">
                          {question.label}
                        </span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {condition.question_id && !selectedQuestion && (
              <p className="text-xs text-amber-600">Selected question not found</p>
            )}
          </div>

          {/* Operator Selection */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Operator</Label>
            <Select
              value={condition.operator}
              onValueChange={handleOperatorChange}
              disabled={!selectedQuestion}
            >
              <SelectTrigger className="h-9 rounded-md border border-border bg-background px-3 text-base focus:ring-2 focus:ring-primary">
                <SelectValue placeholder="Select operator..." />
              </SelectTrigger>
              <SelectContent className="rounded-md py-1">
                {availableOperators.map((operator) => (
                  <SelectItem key={operator.value} value={operator.value} className="px-3 py-2 text-sm">
                    {operator.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Options Selection */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Options</Label>
                         {selectedQuestion ? (
               <ValueInput
                 question={selectedQuestion}
                 operator={condition.operator}
                 value={condition.value}
                 onValueChange={(value) => onUpdate({ value })}
                 placeholder="Select options..."
                 disabled={!selectedQuestion}
               />
             ) : (
              <div className="flex min-h-[32px] w-full items-center rounded-md border border-input bg-muted px-3 py-1 text-sm text-muted-foreground">
                Select question first
              </div>
            )}
            {selectedQuestion && selectedQuestion.type !== 'number' && Array.isArray(condition.value) && condition.value.length === 0 && (
              <p className="text-xs text-amber-600">Select at least one option to save this condition.</p>
            )}
            {selectedQuestion && selectedQuestion.type === 'number' && (!condition.value && condition.value !== 0) && (
              <p className="text-xs text-amber-600">Enter a numeric value to save this condition.</p>
            )}
          </div>
        </div>

        {/* Delete Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="flex items-center justify-center rounded-full text-destructive hover:text-destructive"
          title="Delete condition"
          type="button"
        >
          <Trash2 className="size-5" />
        </Button>
      </Card>
    </motion.div>
  );
} 