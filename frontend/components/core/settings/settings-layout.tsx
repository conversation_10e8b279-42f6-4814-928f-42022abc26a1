"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { motion } from "framer-motion"
import { User, Building2, Users, Settings } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"

interface SettingsLayoutProps {
  children?: React.ReactNode
  activeTab?: string
  className?: string
}

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  adminOnly?: boolean
}

const tabs: TabConfig[] = [
  {
    id: "profile",
    label: "Profile",
    icon: User,
    description: "Manage your personal account settings and preferences"
  },
  {
    id: "organization", 
    label: "Organization",
    icon: Building2,
    description: "Configure your organization's branding and settings",
    adminOnly: true
  },
  {
    id: "members",
    label: "Members", 
    icon: Users,
    description: "Manage team members and user permissions",
    adminOnly: true
  }
]

export function SettingsLayout({
  children,
  activeTab: propActiveTab,
  className
}: SettingsLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useAuth()

  // Determine active tab from URL
  const getActiveTabFromPath = () => {
    if (pathname?.includes('/settings/profile')) return 'profile'
    if (pathname?.includes('/settings/organization')) return 'organization'
    if (pathname?.includes('/settings/members')) return 'members'
    return 'profile'
  }

  const [activeTab, setActiveTab] = useState(propActiveTab || getActiveTabFromPath())

  useEffect(() => {
    const tabFromPath = getActiveTabFromPath()
    setActiveTab(tabFromPath)
  }, [pathname])

  // Check if user is admin (you can adjust this logic based on your auth system)
  const isAdmin = true // TODO: Replace with actual role check based on user data

  const availableTabs = tabs.filter(tab =>
    !tab.adminOnly || isAdmin
  )

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    router.push(`/settings/${tabId}`)
  }

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      <div className="relative">
        {/* Header */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto max-w-screen-xl px-4 py-6 sm:px-6 sm:py-8 lg:px-8">
            <div className="flex items-center space-x-4">
              <div className="flex size-10 items-center justify-center rounded-lg bg-muted sm:size-12">
                <Settings className="size-5 text-muted-foreground sm:size-6" />
              </div>
              <div>
                <h1 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
                  Settings
                </h1>
                <p className="mt-1 text-sm text-muted-foreground sm:text-base">
                  Manage your account, organization, and team preferences
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="container mx-auto max-w-screen-xl px-4 py-6 sm:px-6 sm:py-8 lg:px-8">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6 sm:space-y-8">
            {/* Tab Navigation */}
            <TabsList className="grid w-full grid-cols-1 rounded-lg bg-muted p-1 md:grid-cols-3">
              {availableTabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center justify-center space-x-2 rounded-md px-3 py-2 transition-all duration-200 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                  >
                    <Icon className="size-4" />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {/* Tab Content */}
            <div className="space-y-6 sm:space-y-8">
              {availableTabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-0">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3">
                        <div className="flex size-8 items-center justify-center rounded-lg bg-muted">
                          <tab.icon className="size-4 text-muted-foreground" />
                        </div>
                        <div>
                          <h2 className="text-xl font-semibold text-foreground sm:text-2xl">
                            {tab.label}
                          </h2>
                          <p className="text-sm text-muted-foreground">
                            {tab.description}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-6">
                      {children}
                    </div>
                  </motion.div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Export individual tab content components for use in pages
export function ProfileTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrganizationTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function MembersTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}
