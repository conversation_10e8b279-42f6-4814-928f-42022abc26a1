"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Bug, Eye, EyeOff } from 'lucide-react';

interface ThesisDebugPanelProps {
  thesisState: any; // Accept any type to avoid type conflicts
  isVisible?: boolean;
  onTestScoringRule?: (questionId: string) => void;
}

export function ThesisDebugPanel({ thesisState, isVisible = false, onTestScoringRule }: ThesisDebugPanelProps) {
  const [showDebug, setShowDebug] = useState(isVisible);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!showDebug) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setShowDebug(true)}
          variant="outline"
          size="sm"
          className="bg-background shadow-lg"
        >
          <Bug className="mr-2 size-4" />
          Debug
        </Button>
      </div>
    );
  }

  const scoringRulesCount = thesisState.scoringRules?.length || 0;
  const bonusRulesCount = thesisState.bonusRules?.length || 0;
  const matchRulesCount = thesisState.matchRules?.length || 0;

  return (
    <div className="fixed bottom-4 right-4 z-50 max-h-[80vh] w-96 overflow-y-auto">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Bug className="size-4" />
              Thesis Debug Panel
            </CardTitle>
            <Button
              onClick={() => setShowDebug(false)}
              variant="ghost"
              size="sm"
            >
              <EyeOff className="size-4" />
            </Button>
          </div>
          <CardDescription className="text-xs">
            Real-time thesis state monitoring
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Summary */}
          <div className="grid grid-cols-3 gap-2">
            <div className="rounded bg-blue-50 p-2 text-center">
              <div className="font-medium text-blue-700">{scoringRulesCount}</div>
              <div className="text-blue-600">Scoring</div>
            </div>
            <div className="rounded bg-green-50 p-2 text-center">
              <div className="font-medium text-green-700">{bonusRulesCount}</div>
              <div className="text-green-600">Bonus</div>
            </div>
            <div className="rounded bg-purple-50 p-2 text-center">
              <div className="font-medium text-purple-700">{matchRulesCount}</div>
              <div className="text-purple-600">Match</div>
            </div>
          </div>

          {/* Basic Info */}
          <Collapsible open={expandedSections.basic} onOpenChange={() => toggleSection('basic')}>
            <CollapsibleTrigger className="flex w-full items-center gap-2 rounded p-2 text-left hover:bg-muted">
              {expandedSections.basic ? <ChevronDown className="size-3" /> : <ChevronRight className="size-3" />}
              <span className="font-medium">Basic Info</span>
              <Badge variant="outline" className="ml-auto text-xs">
                {thesisState.thesis.name ? 'Set' : 'Missing'}
              </Badge>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 pl-5 pt-2">
              <div><strong>Name:</strong> {thesisState.thesis.name || 'Not set'}</div>
              <div><strong>Description:</strong> {thesisState.thesis.description ? 'Set' : 'Not set'}</div>
              <div><strong>Form ID:</strong> {thesisState.thesis.form_id || 'Not set'}</div>
              <div><strong>Status:</strong> {thesisState.thesis.status || 'Not set'}</div>
            </CollapsibleContent>
          </Collapsible>

          {/* Scoring Rules */}
          <Collapsible open={expandedSections.scoring} onOpenChange={() => toggleSection('scoring')}>
            <CollapsibleTrigger className="flex w-full items-center gap-2 rounded p-2 text-left hover:bg-muted">
              {expandedSections.scoring ? <ChevronDown className="size-3" /> : <ChevronRight className="size-3" />}
              <span className="font-medium">Scoring Rules</span>
              <Badge variant="outline" className="ml-auto text-xs">
                {scoringRulesCount}
              </Badge>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 pl-5 pt-2">
              {thesisState.scoringRules?.map((rule, index) => (
                <div key={index} className="rounded bg-blue-50 p-2 text-xs">
                  <div><strong>Question ID:</strong> {rule.question_id || 'Missing'}</div>
                  <div><strong>Weight:</strong> {rule.weight || 'Not set'}</div>
                  <div><strong>Rule Type:</strong> {rule.rule_type || 'Missing'}</div>
                  <div><strong>Expected Value:</strong> {rule.expected_value !== undefined ? String(rule.expected_value) : 'Not set'}</div>
                  {rule._id && <div><strong>ID:</strong> {rule._id}</div>}
                </div>
              )) || <div className="text-muted-foreground">No scoring rules</div>}
            </CollapsibleContent>
          </Collapsible>

          {/* Bonus Rules */}
          <Collapsible open={expandedSections.bonus} onOpenChange={() => toggleSection('bonus')}>
            <CollapsibleTrigger className="flex w-full items-center gap-2 rounded p-2 text-left hover:bg-muted">
              {expandedSections.bonus ? <ChevronDown className="size-3" /> : <ChevronRight className="size-3" />}
              <span className="font-medium">Bonus Rules</span>
              <Badge variant="outline" className="ml-auto text-xs">
                {bonusRulesCount}
              </Badge>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 pl-5 pt-2">
              {thesisState.bonusRules?.map((rule, index) => (
                <div key={index} className="rounded bg-green-50 p-2 text-xs">
                  <div><strong>Bonus Points:</strong> {rule.bonus_points || 'Not set'}</div>
                  <div><strong>Rule Type:</strong> {rule.rule_type || 'Missing'}</div>
                  <div><strong>Conditions:</strong> {rule.compound_condition?.conditions?.length || 0} conditions</div>
                  <div><strong>Operator:</strong> {rule.compound_condition?.operator || 'Not set'}</div>
                  {rule._id && <div><strong>ID:</strong> {rule._id}</div>}
                </div>
              )) || <div className="text-muted-foreground">No bonus rules</div>}
            </CollapsibleContent>
          </Collapsible>

          {/* Match Rules */}
          <Collapsible open={expandedSections.match} onOpenChange={() => toggleSection('match')}>
            <CollapsibleTrigger className="flex w-full items-center gap-2 rounded p-2 text-left hover:bg-muted">
              {expandedSections.match ? <ChevronDown className="size-3" /> : <ChevronRight className="size-3" />}
              <span className="font-medium">Match Rules</span>
              <Badge variant="outline" className="ml-auto text-xs">
                {matchRulesCount}
              </Badge>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 pl-5 pt-2">
              {thesisState.matchRules?.map((rule, index) => (
                <div key={index} className="rounded bg-purple-50 p-2 text-xs">
                  <div><strong>Name:</strong> {rule.name || 'Not set'}</div>
                  <div><strong>Operator:</strong> {rule.operator || 'Not set'}</div>
                  <div><strong>Conditions:</strong> {rule.conditions?.length || 0} conditions</div>
                  {rule._id && <div><strong>ID:</strong> {rule._id}</div>}
                </div>
              )) || <div className="text-muted-foreground">No match rules</div>}
            </CollapsibleContent>
          </Collapsible>

          {/* Test Actions */}
          {onTestScoringRule && (
            <div className="space-y-2">
              <div className="text-xs font-medium">Test Actions:</div>
              <Button
                onClick={() => onTestScoringRule('68329cbd5715af8c1e2c8ff5')}
                variant="outline"
                size="sm"
                className="w-full text-xs"
              >
                Test Revenue Scoring Rule
              </Button>
            </div>
          )}

          {/* Validation Status */}
          <Alert className="text-xs">
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium">Validation Status:</div>
                <div className="flex items-center gap-2">
                  <span className={thesisState.thesis.name ? 'text-green-600' : 'text-red-600'}>
                    {thesisState.thesis.name ? '✓' : '✗'} Name
                  </span>
                  <span className={thesisState.thesis.description ? 'text-green-600' : 'text-red-600'}>
                    {thesisState.thesis.description ? '✓' : '✗'} Description
                  </span>
                  <span className={thesisState.thesis.form_id ? 'text-green-600' : 'text-red-600'}>
                    {thesisState.thesis.form_id ? '✓' : '✗'} Form
                  </span>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Raw Data */}
          <Collapsible open={expandedSections.raw} onOpenChange={() => toggleSection('raw')}>
            <CollapsibleTrigger className="flex w-full items-center gap-2 rounded p-2 text-left hover:bg-muted">
              {expandedSections.raw ? <ChevronDown className="size-3" /> : <ChevronRight className="size-3" />}
              <span className="font-medium">Raw Data</span>
              <Badge variant="outline" className="ml-auto text-xs">
                JSON
              </Badge>
            </CollapsibleTrigger>
            <CollapsibleContent className="pl-5 pt-2">
              <pre className="max-h-32 overflow-auto rounded bg-muted p-2 text-xs">
                {JSON.stringify(thesisState, null, 2)}
              </pre>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>
    </div>
  );
}
