"use client"

import React, { useState } from 'react';
import { Save, Target, Calculator, Star, FileText, AlertTriangle, Loader2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FormSelector } from './form-selector';
import { MatchingConfigBuilder } from './matching-config-builder';
import { ScoringConfigTable } from './scoring-config-table';
import { BonusBlocksEditor } from './bonus-blocks-editor';
import { useThesisManager } from '@/lib/hooks/use-thesis-manager';
import { ThesisWithRules, ThesisStatus, ScoringRule, MatchRule, BonusRule } from '@/lib/types/thesis';
import { toast } from '@/components/ui/use-toast';
import { ThesisBuilderErrorBoundary } from './error-boundary';
import { ThesisDebugPanel } from './thesis-debug-panel';
import { ThesisStatusToggle } from './thesis-status-toggle';

interface ThesisBuilderProps {
  initialThesis?: ThesisWithRules;
  onSaveSuccess?: (thesis: ThesisWithRules) => void;
}

export function ThesisBuilder({ initialThesis, onSaveSuccess }: ThesisBuilderProps) {
  console.log('🎯 ThesisBuilder props:', { initialThesis });

  // Local state for editing match rules to avoid hook complexity
  const [localMatchRules, setLocalMatchRules] = useState<Partial<MatchRule>[]>([]);
  const [isLocalMatchRulesInitialized, setIsLocalMatchRulesInitialized] = useState(false);

  // Local state for editing bonus rules to avoid hook complexity
  const [localBonusRules, setLocalBonusRules] = useState<Partial<BonusRule>[]>([]);
  const [isLocalBonusRulesInitialized, setIsLocalBonusRulesInitialized] = useState(false);

  const {
    thesisState,
    questionConfigs,
    isSaving,
    isLoading,
    updateThesis,
    selectForm,
    updateQuestionConfig,
    addMatchRule,
    updateMatchRule,
    removeMatchRule,
    createMatchRule,
    deleteMatchRule,
    addBonusRule,
    updateBonusRule,
    removeBonusRule,
    createScoringRule,
    updateScoringRule,
    deleteScoringRule,
    saveThesis,
    deleteBonusRule,
    createBonusRule,
  } = useThesisManager({
    initialThesis,
    onSaveSuccess
  });

  // Initialize local match rules from thesis state
  React.useEffect(() => {
    if (thesisState.matchRules && !isLocalMatchRulesInitialized) {
      console.log('🎯 Initializing local match rules from thesis state:', thesisState.matchRules);
      setLocalMatchRules([...thesisState.matchRules]);
      setIsLocalMatchRulesInitialized(true);
    }
  }, [thesisState.matchRules, isLocalMatchRulesInitialized]);

  // Sync local match rules when thesis state changes (after API calls)
  React.useEffect(() => {
    if (isLocalMatchRulesInitialized) {
      console.log('🎯 Syncing local match rules with thesis state:', thesisState.matchRules);
      setLocalMatchRules([...thesisState.matchRules]);
    }
  }, [thesisState.matchRules, isLocalMatchRulesInitialized]);

  // Debug logging for local match rules
  React.useEffect(() => {
    console.log('🎯 Local match rules state:', {
      count: localMatchRules.length,
      rules: localMatchRules.map(r => ({ id: r._id || r.id, name: r.name, hasId: !!(r._id || r.id) }))
    });
  }, [localMatchRules]);

  // Initialize local bonus rules from thesis state
  React.useEffect(() => {
    if (thesisState.bonusRules && !isLocalBonusRulesInitialized) {
      setLocalBonusRules([...thesisState.bonusRules]);
      setIsLocalBonusRulesInitialized(true);
    }
  }, [thesisState.bonusRules, isLocalBonusRulesInitialized]);

  // Sync local bonus rules when thesis state changes (after API calls)
  React.useEffect(() => {
    if (isLocalBonusRulesInitialized) {
      setLocalBonusRules([...thesisState.bonusRules]);
    }
  }, [thesisState.bonusRules, isLocalBonusRulesInitialized]);

  // Helper to normalize rule IDs
  const normalizeRuleIds = (rules: any[]) => {
    return rules.map(rule => {
      const normalized = { ...rule };
      // Use id if available, otherwise use _id
      if (!normalized.id && normalized._id) {
        normalized.id = normalized._id;
      }
      // Remove _id to avoid confusion
      if ('_id' in normalized) {
        delete normalized._id;
      }
      return normalized;
    });
  };

  // Handle adding a bonus rule
  const handleAddBonusRule = (rule: any) => {
    const normalizedRule = normalizeRuleIds([rule])[0];
    addBonusRule(normalizedRule);
  };

  // Handle updating a bonus rule (now uses ruleId instead of index)
  const handleUpdateBonusRule = (index: number, updates: any) => {
    // For local bonus rules management, just update the local state
    setLocalBonusRules(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], ...updates };
      return updated;
    });
  };

  // Handle removing a bonus rule
  const handleRemoveBonusRule = (index: number) => {
    setLocalBonusRules(prev => prev.filter((_, i) => i !== index));
  };

  // Local match rules handlers to provide smooth editing experience
  const handleAddLocalMatchRule = (rule: Partial<MatchRule>) => {
    setLocalMatchRules(prev => [...prev, rule]);
  };

  const handleUpdateLocalMatchRule = (index: number, updates: Partial<MatchRule>) => {
    setLocalMatchRules(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], ...updates };
      return updated;
    });
  };

  const handleRemoveLocalMatchRule = (index: number) => {
    setLocalMatchRules(prev => prev.filter((_, i) => i !== index));
  };

  const handleSaveLocalMatchRule = async (rule: Partial<MatchRule>) => {
    try {
      const ruleId = rule._id || rule.id;
      console.log('🎯 handleSaveLocalMatchRule called with:', { rule, ruleId });
      
      if (ruleId) {
        // Update existing rule
        console.log('🎯 Updating existing rule:', ruleId);
        await updateMatchRule(ruleId, rule);
      } else {
        // Create new rule
        console.log('🎯 Creating new rule');
        await createMatchRule(rule);
      }
      console.log('🎯 Rule saved successfully');
      // State will be synced automatically via useEffect
    } catch (error) {
      console.error('Error saving match rule:', error);
      throw error; // Re-throw to let the component handle loading states
    }
  };

  // Local bonus rules handlers to provide smooth editing experience
  const handleAddLocalBonusRule = (rule: Partial<BonusRule>) => {
    setLocalBonusRules(prev => [...prev, rule]);
  };

  const handleUpdateLocalBonusRule = (index: number, updates: Partial<BonusRule>) => {
    setLocalBonusRules(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], ...updates };
      return updated;
    });
  };

  const handleRemoveLocalBonusRule = (index: number) => {
    setLocalBonusRules(prev => prev.filter((_, i) => i !== index));
  };

  const handleSaveLocalBonusRule = async (rule: Partial<BonusRule>) => {
    try {
      const ruleId = rule._id || rule.id;
      if (ruleId) {
        // Update existing rule
        await updateBonusRule(ruleId, rule);
      } else {
        // Create new rule
        await createBonusRule(rule);
      }
      // State will be synced automatically via useEffect
    } catch (error) {
      console.error('Error saving bonus rule:', error);
      throw error; // Re-throw to let the component handle loading states
    }
  };

  const isEditing = !!initialThesis;
  const canSave = thesisState.thesis.name && thesisState.thesis.description && thesisState.thesis.form_id;

  const handleSave = async () => {
    try {
      // Comprehensive validation
      if (!thesisState.thesis.name?.trim()) {
        toast({
          title: "Missing thesis name",
          description: "Please provide a name for your thesis.",
          variant: "destructive",
        });
        return;
      }

      if (!thesisState.thesis.description?.trim()) {
        toast({
          title: "Missing thesis description",
          description: "Please provide a description for your thesis.",
          variant: "destructive",
        });
        return;
      }

      if (!thesisState.thesis.form_id) {
        toast({
          title: "Missing form selection",
          description: "Please select a form for your thesis.",
          variant: "destructive",
        });
        return;
      }

      // Validate match rules if they exist
      for (const rule of thesisState.matchRules) {
        if (!rule.name?.trim()) {
          toast({
            title: "Invalid match rule",
            description: "All match rules must have a name.",
            variant: "destructive",
          });
          return;
        }
      }

      // Validate bonus rules if they exist
      for (const rule of thesisState.bonusRules) {
        if (!rule.bonus_points) {
          toast({
            title: "Invalid bonus rule",
            description: "All bonus rules must have positive bonus points.",
            variant: "destructive",
          });
          return;
        }
      }

      console.log('✅ All validations passed, saving thesis...');
      await saveThesis();
    } catch (error) {
      console.error('❌ Error in handleSave:', error);
      toast({
        title: "Error saving thesis",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleStatusChange = (newStatus: ThesisStatus) => {
    console.log('🎯 Changing thesis status to:', newStatus);
    updateThesis({ status: newStatus });
  };

  return (
    <ThesisBuilderErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-semibold tracking-tight">
              {isEditing ? 'Edit Thesis' : 'Create New Thesis'}
            </h2>
            <p className="text-sm text-muted-foreground">
              Configure your investment thesis scoring and matching rules
            </p>
          </div>
          <div className="flex items-center gap-4">
            <ThesisStatusToggle
              currentStatus={thesisState.thesis.status || ThesisStatus.DRAFT}
              onStatusChange={handleStatusChange}
              disabled={isSaving}
            />
            <Button
              onClick={handleSave}
              disabled={!canSave || isSaving}
              className="flex items-center gap-2"
            >
              {isSaving ? (
                <>
                  <Loader2 className="size-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="size-4" />
                  Save Thesis
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="size-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Provide basic details about your investment thesis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <label className="text-sm font-medium">Thesis Name *</label>
                <Input
                  placeholder="e.g., Early Stage SaaS Thesis"
                  value={thesisState.thesis.name || ''}
                  onChange={(e) => updateThesis({ name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <div className="flex items-center gap-2">
                  <Badge variant={thesisState.thesis.is_active ? 'default' : 'secondary'}>
                    {thesisState.thesis.is_active? "Active" : 'Draft'}
                  </Badge>
                  {/*<span className="text-sm text-muted-foreground">*/}
                  {/*  {thesisState.thesis.is_active ? 'Active' : 'Inactive'}*/}
                  {/*</span>*/}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Description *</label>
              <Textarea
                placeholder="Describe your investment thesis, target criteria, and evaluation approach..."
                value={thesisState.thesis.description || ''}
                onChange={(e) => updateThesis({ description: e.target.value })}
                className="min-h-[100px]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Form Selection */}
        <FormSelector
          selectedFormId={thesisState.thesis.form_id}
          onFormSelect={selectForm}
          disabled={isLoading}
        />

        {/* Configuration Tabs */}
        {thesisState.selectedForm && (
          <Tabs defaultValue="matching" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="matching" className="flex items-center gap-2">
                <Target className="size-4" />
                Matching Rules
              </TabsTrigger>
              <TabsTrigger value="scoring" className="flex items-center gap-2">
                <Calculator className="size-4" />
                Scoring Rules
              </TabsTrigger>
              <TabsTrigger value="bonus" className="flex items-center gap-2">
                <Star className="size-4" />
                Bonus Rules
              </TabsTrigger>
            </TabsList>

            <TabsContent value="matching" className="space-y-4">
              <MatchingConfigBuilder
                matchRules={localMatchRules}
                onAddMatchRule={handleAddLocalMatchRule}
                onUpdateMatchRule={handleUpdateLocalMatchRule}
                onRemoveMatchRule={handleRemoveLocalMatchRule}
                onSaveMatchRule={handleSaveLocalMatchRule}
                onDeleteMatchRule={async (ruleId) => {
                  try {
                    await deleteMatchRule(ruleId);
                    
                    // Update local state to immediately reflect the deletion
                    setLocalMatchRules(prev => prev.filter(rule => 
                      (rule._id !== ruleId) && (rule.id !== ruleId)
                    ));
                    
                    toast({
                      title: "Success",
                      description: "Match rule deleted successfully.",
                    });
                  } catch (error) {
                    console.error('Error deleting match rule:', error);
                    toast({
                      title: "Error deleting match rule",
                      description: "Failed to delete match rule. Please try again.",
                      variant: "destructive",
                    });
                    throw error; // Re-throw to let the component handle loading states
                  }
                }}
                form={thesisState.selectedForm}
              />
            </TabsContent>

            <TabsContent value="scoring" className="space-y-4">
              <ScoringConfigTable
                questionConfigs={questionConfigs}
                onQuestionConfigChange={updateQuestionConfig}
                onCreateScoringRule={createScoringRule}
                onUpdateScoringRule={updateScoringRule}
                onDeleteScoringRule={deleteScoringRule}
                existingScoringRules={thesisState.scoringRules as ScoringRule[]}
                form={thesisState.selectedForm}
                thesisId={thesisState.thesis._id as string}
              />
            </TabsContent>

            <TabsContent value="bonus" className="space-y-4">
              <BonusBlocksEditor
                bonusRules={localBonusRules}
                onAddBonusRule={handleAddLocalBonusRule}
                onUpdateBonusRule={handleUpdateLocalBonusRule}
                onRemoveBonusRule={handleRemoveLocalBonusRule}
                onCreateBonusRule={createBonusRule}
                onSaveBonusRule={async (ruleId: string, rule: Partial<BonusRule>) => {
                  try {
                    await updateBonusRule(ruleId, rule);
                  } catch (error) {
                    console.error('Error saving bonus rule:', error);
                    throw error; // Re-throw to let the component handle loading states
                  }
                }}
                onDeleteBonusRule={async (index, rule) => {
                  try {
                    const ruleId = rule._id || rule.id;
                    if (ruleId && !ruleId.toString().startsWith('temp-')) {
                      await deleteBonusRule(ruleId);
                      
                      // Update local state to immediately reflect the deletion
                      setLocalBonusRules(prev => prev.filter(localRule => 
                        (localRule._id !== ruleId) && (localRule.id !== ruleId)
                      ));
                      
                      toast({
                        title: "Success",
                        description: "Bonus rule deleted successfully.",
                      });
                    }
                  } catch (error) {
                    console.error('Error deleting bonus rule:', error);
                    toast({
                      title: "Error deleting bonus rule",
                      description: "Failed to delete bonus rule. Please try again.",
                      variant: "destructive",
                    });
                    throw error; // Re-throw to let the component handle loading states
                  }
                }}
                form={thesisState.selectedForm}
              />
            </TabsContent>
          </Tabs>
        )}

        {/* Summary */}
        {thesisState.selectedForm && (
          <Card>
            <CardHeader>
              <CardTitle>Configuration Summary</CardTitle>
              <CardDescription>
                Review your thesis configuration before saving
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Target className="size-4" />
                    <span className="font-medium">Matching Rules</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {(thesisState.matchRules || []).length > 0
                      ? `${(thesisState.matchRules || []).length} rule(s) with ${(thesisState.matchRules || []).reduce((total, rule) => total + ((rule?.conditions || []).length || 0), 0)} total condition(s)`
                      : 'No filtering (all deals eligible)'
                    }
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calculator className="size-4" />
                    <span className="font-medium">Scoring Rules</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {questionConfigs.filter(config => config.included).length} questions included
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Star className="size-4" />
                    <span className="font-medium">Bonus Rules</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {(thesisState.bonusRules || []).length} bonus rule(s) •
                    +{(thesisState.bonusRules || []).reduce((total, rule) => total + ((rule?.bonus_points || 0)), 0)} max points
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Debug Panel */}
      <ThesisDebugPanel
        thesisState={thesisState as any}
        onTestScoringRule={(questionId) => {
          console.log('🧪 Testing scoring rule for question:', questionId);
          updateQuestionConfig(questionId, { included: true, weight: 5 });
        }}
      />
    </ThesisBuilderErrorBoundary>
  );
}
