import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';
import { ThesisStatus } from '@/lib/types/thesis';

interface ThesisStatusToggleProps {
  currentStatus: ThesisStatus;
  onStatusChange: (status: ThesisStatus) => void;
  disabled?: boolean;
}

const statusConfig = {
  [ThesisStatus.DRAFT]: {
    label: 'Draft',
    description: 'Thesis is in draft mode and not yet active',
    variant: 'secondary' as const,
  },
  [ThesisStatus.ACTIVE]: {
    label: 'Active',
    description: 'Thesis is active and being used for scoring',
    variant: 'success' as const,
  },
  [ThesisStatus.ARCHIVED]: {
    label: 'Archived',
    description: 'Thesis is archived and no longer in use',
    variant: 'destructive' as const,
  },
};

export function ThesisStatusToggle({ currentStatus, onStatusChange, disabled = false }: ThesisStatusToggleProps) {
  const config = statusConfig[currentStatus];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button variant="outline" className="flex items-center gap-2">
          <Badge className={`${config.variant} text-white`}>{config.label.toUpperCase()}</Badge>
          <ChevronDown className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {Object.entries(statusConfig).map(([status, { label, description }]) => (
          <DropdownMenuItem
            key={status}
            onClick={() => onStatusChange(status as ThesisStatus)}
            disabled={status === currentStatus || disabled}
            className="flex flex-col items-start gap-1"
          >
            <span className="font-medium">{label}</span>
            <span className="text-xs text-muted-foreground">{description}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 