"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, Lightbulb, Linkedin } from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

export function FoundersTabMinimal({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()

  // Keep local founders in sync with deal prop if it changes
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  if (founders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-muted">
          <Users className="size-12" />
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Team Overview Header */}
      <Card className="border-0 bg-background/60 shadow-sm backdrop-blur-md">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Users className="size-5 text-muted-foreground" />
                <span className="text-2xl font-bold">{founders.length}</span>
                <span className="text-sm text-muted-foreground">
                  Founder{founders.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                onClick={() => toast({ title: "AI Summary", description: "Feature coming soon!" })}
                className="gap-2"
                size="sm"
              >
                <Lightbulb className="size-4" />
                Generate Summary
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Founder Cards */}
      <div className="space-y-6">
        {founders.map((founder, index) => (
          <Card key={founder._id || index} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Avatar className="size-20">
                  <AvatarImage 
                    src={founder.profile_picture || undefined} 
                    alt={founder.name || 'Founder'} 
                  />
                  <AvatarFallback className="text-lg">
                    {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="min-w-0 flex-1">
                  <h3 className="text-xl font-bold">
                    {founder.name || 'Unknown Founder'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                  </p>
                  
                  {/* Social Links */}
                  <div className="mt-3 flex items-center gap-2">
                    {founder.linkedin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSocialClick(founder.linkedin!)}
                        className="h-8 gap-2"
                      >
                        <Linkedin className="size-4" />
                        LinkedIn
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
