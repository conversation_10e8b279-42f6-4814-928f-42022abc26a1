"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, TrendingUp, Lightbulb } from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

export function FoundersTabBasic({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()

  // Keep local founders in sync with deal prop if it changes
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  const generateTeamSummary = async () => {
    toast({
      title: "AI Summary",
      description: "Feature coming soon!",
    })
  }

  if (founders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-muted">
          <Users className="size-12" />
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    )
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className="space-y-6">
      {/* Team Overview Header */}
      <Card className="border-0 bg-background/60 shadow-sm backdrop-blur-md">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Users className="size-5 text-muted-foreground" />
                <span className="text-2xl font-bold">{founders.length}</span>
                <span className="text-sm text-muted-foreground">
                  Founder{founders.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                onClick={generateTeamSummary}
                className="gap-2"
                size="sm"
              >
                <Lightbulb className="size-4" />
                Generate Summary
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Founder Cards */}
      <div className="space-y-6">
        {founders.map((founder, index) => (
          <Card key={founder._id || index} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Avatar className="size-20">
                  <AvatarImage 
                    src={founder.profile_picture || undefined} 
                    alt={founder.name || 'Founder'} 
                  />
                  <AvatarFallback className="text-lg">
                    {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="min-w-0 flex-1">
                  <h3 className="text-xl font-bold">
                    {founder.name || 'Unknown Founder'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                  </p>
                  
                  {/* Social Links */}
                  <div className="mt-3 flex items-center gap-2">
                    {founder.linkedin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSocialClick(founder.linkedin!)}
                        className="h-8 gap-2"
                      >
                        <svg className="size-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
