"use client"

import { useState, useEffect } from "react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { <PERSON> } from "@/lib/types/deal"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

export function FoundersTabBasic({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])

  // Keep local founders in sync with deal prop if it changes
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  if (founders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-gray-100">
          <svg className="size-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-gray-600">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Team Overview Header */}
      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <svg className="size-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <span className="text-2xl font-bold">{founders.length}</span>
              <span className="text-sm text-gray-500">
                Founder{founders.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <button className="inline-flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
              <svg className="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              Generate Summary
            </button>
          </div>
        </div>
      </div>

      {/* Founder Cards */}
      <div className="space-y-6">
        {founders.map((founder, index) => (
          <div key={founder._id || index} className="rounded-lg border bg-white p-6 shadow-sm">
            <div className="flex items-start gap-4">
              <div className="flex size-20 items-center justify-center rounded-full bg-gray-200 text-lg font-semibold text-gray-600">
                {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
              </div>
              
              <div className="min-w-0 flex-1">
                <h3 className="text-xl font-bold text-gray-900">
                  {founder.name || 'Unknown Founder'}
                </h3>
                <p className="text-sm text-gray-600">
                  {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                </p>
                
                {/* Social Links */}
                <div className="mt-3 flex items-center gap-2">
                  {founder.linkedin && (
                    <button
                      onClick={() => window.open(founder.linkedin!, '_blank', 'noopener,noreferrer')}
                      className="inline-flex items-center gap-2 px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
                    >
                      <svg className="size-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                      LinkedIn
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
