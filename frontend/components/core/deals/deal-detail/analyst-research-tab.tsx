"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { 
  Search, 
  TrendingUp, 
  Newspaper, 
  FileText, 
  Download,
  RefreshCw,
  ExternalLink,
  Copy,
  CheckCircle,
  AlertCircle,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, AnalystResearchData } from "@/lib/types/deal-detail"
import { ExternalSignalsAPI } from "@/lib/api/external-signals-api"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { BackgroundPattern } from "@/components/ui/background-pattern"
import { ShimmerGrid } from "@/components/ui/shimmer-skeleton"
import { useAnalystResearch } from "@/lib/hooks/use-analyst-research"

interface AnalystResearchTabProps {
  deal: DealDetailData
}

const researchTabs = [
  {
    id: 'competitors',
    label: 'Competitors',
    icon: Search,
    description: 'Competitive landscape analysis'
  },
  {
    id: 'market',
    label: 'Market',
    icon: TrendingUp,
    description: 'Market trends and insights'
  },
  {
    id: 'news',
    label: 'News',
    icon: Newspaper,
    description: 'Recent signals and developments'
  },
  {
    id: 'summary',
    label: 'Executive Summary',
    icon: FileText,
    description: 'Investment narrative synthesis'
  }
]

export function AnalystResearchTab({ deal }: AnalystResearchTabProps) {
  const [activeTab, setActiveTab] = useState('competitors')
  const [copiedSummary, setCopiedSummary] = useState(false)

  // Use the research hook for data management
  const {
    data: researchData,
    loading,
    error,
    refreshing,
    hasData,
    refresh: handleRefresh,
    reload
  } = useAnalystResearch({ dealId: deal.id })

  // Auto-select first available tab when data loads
  useEffect(() => {
    if (researchData) {
      if (researchData.competitors) setActiveTab('competitors')
      else if (researchData.market) setActiveTab('market')
      else if (researchData.news) setActiveTab('news')
      else if (researchData.summary) setActiveTab('summary')
    }
  }, [researchData])

  const handleCopySummary = async () => {
    if (!researchData?.summary?.executive_summary) return

    try {
      await navigator.clipboard.writeText(researchData.summary.executive_summary)
      setCopiedSummary(true)

      setTimeout(() => setCopiedSummary(false), 2000)
    } catch (err) {
      console.error('Error copying summary:', err)
    }
  }

  const getTabCount = (tabId: string) => {
    if (!researchData) return 0
    
    switch (tabId) {
      case 'competitors':
        return researchData.competitors?.competitors?.length || 0
      case 'market':
        return researchData.market?.market_trends?.length || 0
      case 'news':
        return researchData.news?.news_signals?.length || 0
      case 'summary':
        return researchData.summary ? 1 : 0
      default:
        return 0
    }
  }

  // Use hasData from the hook

  if (loading) {
    return (
      <div className="relative min-h-screen">
        <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
        <div className="relative space-y-8">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="mb-2 h-8 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex gap-3">
              <Skeleton className="h-9 w-20" />
              <Skeleton className="h-9 w-24" />
            </div>
          </div>

          {/* Tabs Skeleton */}
          <div className="grid grid-cols-4 gap-2 rounded-xl border bg-white/80 p-1 shadow-lg backdrop-blur-md">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex flex-col items-center gap-2 px-3 py-4">
                <Skeleton className="size-5 rounded" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>

          {/* Content Skeleton */}
          <ShimmerGrid count={6} />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Research Unavailable</h3>
            <p className="mt-1 text-sm text-gray-600">{error}</p>
          </div>
          <Button onClick={reload} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (!hasData) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="search" />
        <EmptyPlaceholder.Title>No research available yet</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Our analysts are preparing comprehensive research for this deal. 
          Check back after the context enrichment is complete, or refresh to generate new insights.
        </EmptyPlaceholder.Description>
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="mt-4"
        >
          {refreshing ? (
            <>
              <RefreshCw className="mr-2 size-4 animate-spin" />
              Generating Research...
            </>
          ) : (
            <>
              <Zap className="mr-2 size-4" />
              Generate Research
            </>
          )}
        </Button>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="relative min-h-screen">
      {/* Subtle Background Pattern */}
      <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />

      <div className="relative space-y-8">
        {/* Research Header */}
        <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analyst Research</h2>
          <p className="mt-1 text-sm text-gray-600">
            Comprehensive market intelligence and competitive analysis
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="gap-2"
          >
            <RefreshCw className={cn("size-4", refreshing && "animate-spin")} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <Download className="size-4" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Research Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Floating Tab Navigation */}
        <div className="relative">
          {/* Desktop Tabs */}
          <TabsList className="hidden h-auto w-full grid-cols-4 rounded-xl border bg-white/80 p-1 shadow-lg backdrop-blur-md md:grid">
            {researchTabs.map((tab) => {
              const Icon = tab.icon
              const count = getTabCount(tab.id)
              const hasData = count > 0

              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  disabled={!hasData}
                  className="flex flex-col items-center gap-2 rounded-lg px-3 py-4 transition-all duration-200 disabled:opacity-50 data-[state=active]:bg-primary data-[state=active]:text-white"
                >
                  <Icon className="size-5" />
                  <div className="text-center">
                    <div className="text-sm font-medium">{tab.label}</div>
                    {hasData && (
                      <Badge
                        variant="secondary"
                        className="mt-1 h-5 min-w-[20px] rounded-full px-1.5 text-xs"
                      >
                        {count}
                      </Badge>
                    )}
                  </div>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {/* Mobile Tabs - Horizontal Scroll */}
          <div className="md:hidden">
            <TabsList className="flex h-auto w-full overflow-x-auto rounded-xl border bg-white/80 p-1 shadow-lg backdrop-blur-md">
              {researchTabs.map((tab) => {
                const Icon = tab.icon
                const count = getTabCount(tab.id)
                const hasData = count > 0

                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    disabled={!hasData}
                    className="flex min-w-fit items-center gap-2 whitespace-nowrap rounded-lg px-4 py-3 transition-all duration-200 disabled:opacity-50 data-[state=active]:bg-primary data-[state=active]:text-white"
                  >
                    <Icon className="size-4" />
                    <span className="text-sm font-medium">{tab.label}</span>
                    {hasData && (
                      <Badge
                        variant="secondary"
                        className="h-5 min-w-[20px] rounded-full px-1.5 text-xs"
                      >
                        {count}
                      </Badge>
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Competitors Tab */}
              <TabsContent value="competitors" className="mt-0">
                {researchData?.competitors && (
                  <CompetitorsCard competitors={researchData.competitors.competitors} />
                )}
              </TabsContent>

              {/* Market Tab */}
              <TabsContent value="market" className="mt-0">
                {researchData?.market && (
                  <MarketCard trends={researchData.market.market_trends} />
                )}
              </TabsContent>

              {/* News Tab */}
              <TabsContent value="news" className="mt-0">
                {researchData?.news && (
                  <NewsCard signals={researchData.news.news_signals} />
                )}
              </TabsContent>

              {/* Executive Summary Tab */}
              <TabsContent value="summary" className="mt-0">
                {researchData?.summary && (
                  <ExecutiveSummaryCard 
                    summary={researchData.summary.executive_summary}
                    sources={researchData.summary.sources}
                    onCopy={handleCopySummary}
                    copied={copiedSummary}
                  />
                )}
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </div>
      </Tabs>
      </div>
    </div>
  )
}

// Competitors Card Component
function CompetitorsCard({ competitors }: { competitors: any[] }) {
  return (
    <div className="grid gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3">
      {competitors.map((competitor, index) => (
        <motion.div
          key={competitor.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="h-full border-0 bg-white/80 shadow-xl backdrop-blur-md transition-all duration-300 hover:scale-105 hover:shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="mb-2 text-lg font-bold text-gray-900">
                    {competitor.name}
                  </CardTitle>
                  {competitor.website && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-blue-600 hover:text-blue-800"
                      onClick={() => window.open(competitor.website, '_blank')}
                    >
                      <ExternalLink className="mr-1 size-3" />
                      Visit Website
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-sm leading-relaxed text-gray-700">
                {competitor.description}
              </p>

              <div className="rounded-lg border-l-4 border-primary/30 bg-muted/40 px-4 py-3">
                <p className="text-sm font-medium text-gray-900">
                  {competitor.comparison}
                </p>
              </div>

              {competitor.sources && competitor.sources.length > 0 && (
                <div className="border-t border-gray-100 pt-2">
                  <p className="mb-2 text-xs text-gray-500">Sources:</p>
                  <div className="flex flex-wrap gap-1">
                    {competitor.sources.slice(0, 2).map((source: string, idx: number) => (
                      <Button
                        key={idx}
                        variant="ghost"
                        size="sm"
                        className="h-auto p-1 text-xs text-blue-600 hover:text-blue-800"
                        onClick={() => window.open(source, '_blank')}
                      >
                        <ExternalLink className="mr-1 size-2" />
                        {new URL(source).hostname}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

// Market Card Component
function MarketCard({ trends }: { trends: any[] }) {
  return (
    <div className="space-y-6">
      {trends.map((trend, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
            <CardContent className="p-8">
              <div className="flex items-start gap-4">
                <div className="mt-3 size-2 shrink-0 rounded-full bg-primary"></div>
                <div className="flex-1">
                  <p className="mb-4 text-base leading-relaxed text-gray-900">
                    {trend.summary}
                  </p>

                  {trend.sources && trend.sources.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Sources:</span>
                      {trend.sources.slice(0, 3).map((source: string, idx: number) => (
                        <Button
                          key={idx}
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 text-blue-600 underline-offset-4 hover:text-blue-800 hover:underline"
                          onClick={() => window.open(source, '_blank')}
                        >
                          {new URL(source).hostname}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

// News Card Component
function NewsCard({ signals }: { signals: any[] }) {
  return (
    <div className="space-y-4">
      {signals.map((signal, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
        >
          <Card className="border-0 bg-white/80 shadow-lg backdrop-blur-md transition-all duration-200 hover:shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-start justify-between gap-4">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-2 font-semibold leading-tight text-gray-900">
                    {signal.headline}
                  </h4>
                  <p className="mb-3 text-sm leading-relaxed text-gray-700">
                    {signal.summary}
                  </p>

                  <div className="flex items-center justify-between">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-blue-600 hover:text-blue-800"
                      onClick={() => window.open(signal.url, '_blank')}
                    >
                      <ExternalLink className="mr-1 size-3" />
                      Read Full Article
                    </Button>

                    {signal.date && (
                      <span className="text-xs text-gray-500">
                        {new Date(signal.date).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>

                <div className="shrink-0">
                  <CheckCircle className="size-4 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

// Executive Summary Card Component
function ExecutiveSummaryCard({
  summary,
  sources,
  onCopy,
  copied
}: {
  summary: string
  sources: string[]
  onCopy: () => void
  copied: boolean
}) {
  return (
    <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-gray-900">
            Investment Narrative
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={onCopy}
            className="gap-2"
          >
            {copied ? (
              <>
                <CheckCircle className="size-4 text-green-600" />
                Copied
              </>
            ) : (
              <>
                <Copy className="size-4" />
                Copy
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="prose prose-gray max-w-none">
          <p className="text-base leading-relaxed text-gray-900">
            {summary}
          </p>
        </div>

        {sources && sources.length > 0 && (
          <div className="border-t border-gray-100 pt-4">
            <p className="mb-3 text-sm font-medium text-gray-900">Sources:</p>
            <div className="flex flex-wrap gap-2">
              {sources.map((source, idx) => (
                <Button
                  key={idx}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => window.open(source, '_blank')}
                >
                  <ExternalLink className="mr-1 size-3" />
                  {new URL(source).hostname}
                </Button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}


