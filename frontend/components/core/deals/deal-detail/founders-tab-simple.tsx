import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Linkedin, 
  Twitter, 
  Github, 
  ExternalLink,
  Star,
  Building,
  Award,
  Edit2,
  Check,
  X,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Download,
  Copy,
  HelpCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DealDetailData } from "@/lib/types/deal-detail";
import { Founder } from "@/lib/types/deal";
import { DealAPI } from "@/lib/api/deal-api";
import { useToast } from "@/components/ui/use-toast";

interface FoundersTabProps {
  deal: DealDetailData;
  onDealUpdate?: (deal: DealDetailData) => void;
}

// Type guard for enriched founder
function isEnrichedFounder(f: any): f is { signals: { score?: number; tags?: string[] } } {
  return f && typeof f === 'object' && 'signals' in f && typeof f.signals === 'object';
}

export default function FoundersTabSimple(props: FoundersTabProps) {
  // Defensive: check all critical imports
  if (
    typeof Card === 'undefined' ||
    typeof CardContent === 'undefined' ||
    typeof Button === 'undefined' ||
    typeof Badge === 'undefined' ||
    typeof motion === 'undefined' ||
    typeof Users === 'undefined'
  ) {
    return <div style={{color: 'red', fontWeight: 'bold'}}>❌ One or more UI imports are undefined. Check your UI library exports and import paths.</div>;
  }

  const [founders, setFounders] = useState<Founder[]>(props.deal.founders || []);
  const { toast } = useToast ? useToast() : { toast: () => {} };
  const [editingFounderIndex, setEditingFounderIndex] = useState<number | null>(null);
  const [editingLinkedIn, setEditingLinkedIn] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [analysisState, setAnalysisState] = useState<{
    enrichedFounders: Founder[];
    isLoading: boolean;
    error: string | null;
    teamSummary: string | null;
    isGeneratingSummary: boolean;
  }>({
    enrichedFounders: [],
    isLoading: false,
    error: null,
    teamSummary: null,
    isGeneratingSummary: false
  });

  useEffect(() => {
    setFounders(props.deal.founders || []);
  }, [props.deal.founders]);

  // Generate team summary
  const generateTeamSummary = async () => {
    if (toast) {
      toast({
        title: "AI Summary",
        description: "Feature coming soon!",
      });
    }
  };

  const teamMetrics = {
    totalFounders: analysisState.enrichedFounders.length || founders.length,
    avgScore: analysisState.enrichedFounders.length > 0 
      ? Math.round(
          analysisState.enrichedFounders.reduce(
            (sum, f) => sum + (isEnrichedFounder(f) && typeof f.signals.score === 'number' ? f.signals.score : 0),
            0
          ) / analysisState.enrichedFounders.length
        )
      : 0,
    topTags: analysisState.enrichedFounders.length > 0
      ? Array.from(
          new Set(
            analysisState.enrichedFounders.flatMap(f =>
              isEnrichedFounder(f) && Array.isArray(f.signals.tags) ? f.signals.tags : []
            )
          )
        ).slice(0, 3)
      : []
  };

  if (founders.length === 0 && analysisState.enrichedFounders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-muted">
          <Users className="size-12" />
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    );
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Team Overview Header - Sticky */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm"
        >
          <Card className="border-0 bg-background/60 shadow-sm backdrop-blur-md">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Users className="size-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">{teamMetrics.totalFounders}</span>
                    <span className="text-sm text-muted-foreground">
                      Founder{teamMetrics.totalFounders !== 1 ? 's' : ''}
                    </span>
                  </div>
                  {teamMetrics.avgScore > 0 && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="size-5 text-muted-foreground" />
                      <span className="text-2xl font-bold">{teamMetrics.avgScore}</span>
                      <span className="text-sm text-muted-foreground">/100</span>
                      <Badge variant="outline" className="text-xs">Avg Score</Badge>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  {teamMetrics.topTags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {teamMetrics.topTags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <Button
                    onClick={generateTeamSummary}
                    disabled={analysisState.isGeneratingSummary}
                    className="gap-2"
                    size="sm"
                  >
                    {/* Removed Brain icon due to missing export */}
                    {analysisState.isGeneratingSummary ? 'Generating...' : 'Generate Summary'}
                  </Button>
                </div>
              </div>
              {analysisState.teamSummary && (
                <div className="mt-4 rounded-lg border bg-muted/50 p-4">
                  <p className="text-sm text-muted-foreground">{analysisState.teamSummary}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
        {/* Simple Founder Cards */}
        <div className="space-y-8">
          {analysisState.isLoading ? (
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">Loading enriched founder data...</p>
            </div>
          ) : analysisState.error ? (
            <div className="py-8 text-center">
              <p className="mb-4 text-sm text-destructive">{analysisState.error}</p>
              <p className="text-xs text-muted-foreground">Showing basic founder information instead</p>
            </div>
          ) : null}
          {/* Show basic founder cards */}
          {founders.map((founder, index) => (
            <motion.div
              key={founder._id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Avatar className="size-20">
                      <AvatarImage 
                        src={founder.profile_picture || undefined} 
                        alt={founder.name || 'Founder'} 
                      />
                      <AvatarFallback className="text-lg">
                        {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-xl font-bold">
                        {founder.name || 'Unknown Founder'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                      </p>
                      {/* Social Links */}
                      <div className="mt-3 flex items-center gap-2">
                        {founder.linkedin && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSocialClick(founder.linkedin!)}
                            className="h-8 gap-2"
                          >
                            <Linkedin className="size-4" />
                            LinkedIn
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
}
