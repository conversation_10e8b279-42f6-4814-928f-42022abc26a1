"use client"

import { motion } from "framer-motion"
import { use<PERSON>tate, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Linkedin, 
  Twitter, 
  Github, 
  ExternalLink,
  Star,
  Building,
  Award,
  Edit2,
  Check,
  X,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Brain,
  Download,
  Copy,
  Sparkles,
  HelpCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { <PERSON> } from "@/lib/types/deal"

import { DealAPI } from "@/lib/api/deal-api"
// import { FounderAPI, EnrichedFounder, FounderAnalysisResponse } from "@/lib/api/founder-api"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

// Enhanced founder analysis state
interface FounderAnalysisState {
  enrichedFounders: any[]
  isLoading: boolean
  error: string | null
  teamSummary: string | null
  isGeneratingSummary: boolean
}

const getScoreColor = (score?: number) => {
  if (!score) return 'text-muted-foreground'
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBackground = (score?: number) => {
  if (!score) return 'bg-gray-50'
  if (score >= 80) return 'bg-green-50'
  if (score >= 60) return 'bg-yellow-50'
  return 'bg-red-50'
}

// LinkedIn URL validation
const isValidLinkedInUrl = (url: string): boolean => {
  if (!url) return false
  const linkedinPattern = /^https?:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/
  return linkedinPattern.test(url)
}

export function FoundersTabSimple({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()
  const [editingFounderIndex, setEditingFounderIndex] = useState<number | null>(null)
  const [editingLinkedIn, setEditingLinkedIn] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)
  
  // Enhanced founder analysis state
  const [analysisState, setAnalysisState] = useState<FounderAnalysisState>({
    enrichedFounders: [],
    isLoading: false,
    error: null,
    teamSummary: null,
    isGeneratingSummary: false
  })

  // Keep local founders in sync with deal prop if it changes (e.g. after parent update)
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  // Fetch enriched founder data
  useEffect(() => {
    // Commented out for now to fix import issues
    // const fetchEnrichedFounders = async () => {
    //   if (!deal.id) return
    //
    //   setAnalysisState(prev => ({ ...prev, isLoading: true, error: null }))
    //
    //   try {
    //     const response = await FounderAPI.getDealFounders(deal.id)
    //     setAnalysisState(prev => ({
    //       ...prev,
    //       enrichedFounders: response.founders,
    //       isLoading: false
    //     }))
    //   } catch (error) {
    //     console.error('Failed to fetch enriched founders:', error)
    //     setAnalysisState(prev => ({
    //       ...prev,
    //       error: 'Failed to load enriched founder data',
    //       isLoading: false
    //     }))
    //   }
    // }

    // fetchEnrichedFounders()
  }, [deal.id])

  // Generate team summary
  const generateTeamSummary = async () => {
    // Commented out for now to fix import issues
    toast({
      title: "AI Summary",
      description: "Feature coming soon!",
    })
  }

  // Calculate team metrics
  const teamMetrics = {
    totalFounders: analysisState.enrichedFounders.length || founders.length,
    avgScore: analysisState.enrichedFounders.length > 0 
      ? Math.round(analysisState.enrichedFounders.reduce((sum, f) => sum + (f.signals?.score || 0), 0) / analysisState.enrichedFounders.length)
      : 0,
    topTags: analysisState.enrichedFounders.length > 0
      ? [...new Set(analysisState.enrichedFounders.flatMap(f => f.signals?.tags || []))].slice(0, 3)
      : []
  }

  if (founders.length === 0 && analysisState.enrichedFounders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-muted">
          <Users className="size-12" />
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    )
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Team Overview Header - Sticky */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm"
        >
          <Card className="border-0 bg-background/60 shadow-sm backdrop-blur-md">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Users className="size-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">{teamMetrics.totalFounders}</span>
                    <span className="text-sm text-muted-foreground">
                      Founder{teamMetrics.totalFounders !== 1 ? 's' : ''}
                    </span>
                  </div>
                  
                  {teamMetrics.avgScore > 0 && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="size-5 text-muted-foreground" />
                      <span className="text-2xl font-bold">{teamMetrics.avgScore}</span>
                      <span className="text-sm text-muted-foreground">/100</span>
                      <Badge variant="outline" className="text-xs">Avg Score</Badge>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-3">
                  {teamMetrics.topTags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {teamMetrics.topTags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <Button
                    onClick={generateTeamSummary}
                    disabled={analysisState.isGeneratingSummary}
                    className="gap-2"
                    size="sm"
                  >
                    <Brain className="size-4" />
                    {analysisState.isGeneratingSummary ? 'Generating...' : 'Generate Summary'}
                  </Button>
                </div>
              </div>
              
              {analysisState.teamSummary && (
                <div className="mt-4 rounded-lg border bg-muted/50 p-4">
                  <p className="text-sm text-muted-foreground">{analysisState.teamSummary}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Simple Founder Cards */}
        <div className="space-y-8">
          {analysisState.isLoading ? (
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">Loading enriched founder data...</p>
            </div>
          ) : analysisState.error ? (
            <div className="py-8 text-center">
              <p className="mb-4 text-sm text-destructive">{analysisState.error}</p>
              <p className="text-xs text-muted-foreground">Showing basic founder information instead</p>
            </div>
          ) : null}

          {/* Show basic founder cards */}
          {founders.map((founder, index) => (
            <motion.div
              key={founder._id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Avatar className="size-20">
                      <AvatarImage 
                        src={founder.profile_picture} 
                        alt={founder.name || 'Founder'} 
                      />
                      <AvatarFallback className="text-lg">
                        {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="min-w-0 flex-1">
                      <h3 className="text-xl font-bold">
                        {founder.name || 'Unknown Founder'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                      </p>
                      
                      {/* Social Links */}
                      <div className="mt-3 flex items-center gap-2">
                        {founder.linkedin && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSocialClick(founder.linkedin!)}
                            className="h-8 gap-2"
                          >
                            <Linkedin className="size-4" />
                            LinkedIn
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  )
}
