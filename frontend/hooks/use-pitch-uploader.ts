import { useState, useCallback } from 'react'
import { DealPitchUploadAPI } from '@/lib/api/deal-pitch-upload-api'

export type UploadStatus = 'idle' | 'preparing' | 'uploading' | 'processing' | 'completed' | 'error'

export interface PitchUploadState {
  status: UploadStatus
  progress: number
  error: string | null
  file: File | null
}

export interface UsePitchUploaderReturn {
  uploadState: PitchUploadState
  uploadPitch: (file: File) => Promise<void>
  resetUpload: () => void
  isUploading: boolean
}

export function usePitchUploader(dealId: string): UsePitchUploaderReturn {
  const [uploadState, setUploadState] = useState<PitchUploadState>({
    status: 'idle',
    progress: 0,
    error: null,
    file: null
  })

  const uploadPitch = useCallback(async (file: File) => {
    // Validate file type
    if (file.type !== 'application/pdf') {
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: 'Only PDF files are supported for pitch decks.'
      }))
      return
    }

    // Validate file size (20MB limit as per PRD)
    const maxSize = 20 * 1024 * 1024 // 20MB
    if (file.size > maxSize) {
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: 'File size must be less than 20MB.'
      }))
      return
    }

    // Get auth tokens
    const token = localStorage.getItem('token')
    const orgId = localStorage.getItem('orgId')

    if (!token || !orgId) {
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: 'Authentication required. Please log in again.'
      }))
      return
    }

    setUploadState(prev => ({
      ...prev,
      status: 'preparing',
      progress: 0,
      error: null,
      file
    }))

    try {
      await DealPitchUploadAPI.uploadPitchComplete(
        dealId,
        file,
        token,
        orgId,
        (progress, status) => {
          setUploadState(prev => ({
            ...prev,
            progress,
            status: status as UploadStatus
          }))
        }
      )

      // Success - show processing message
      setUploadState(prev => ({
        ...prev,
        status: 'completed',
        progress: 100
      }))

    } catch (error: any) {
      console.error('Pitch upload failed:', error)
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: error?.message || 'Upload failed. Please try again.'
      }))
    }
  }, [dealId])

  const resetUpload = useCallback(() => {
    setUploadState({
      status: 'idle',
      progress: 0,
      error: null,
      file: null
    })
  }, [])

  const isUploading = uploadState.status === 'preparing' || 
                     uploadState.status === 'uploading' || 
                     uploadState.status === 'processing'

  return {
    uploadState,
    uploadPitch,
    resetUpload,
    isUploading
  }
} 