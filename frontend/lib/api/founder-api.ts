/**
 * Founder Analysis API Client
 * Handles fetching enriched founder data and triggering enrichment pipelines
 */

import apiClient from "../api-client"



// Types for founder analysis data
export interface FounderSignals {
  score: number
  tags: string[]
  strengths: {
    items: string[]
  }
  risks: {
    items: string[]
  }
  skillProfile: {
    tech: number
    product: number
    business: number
    operations: number
    fundraising: number
  }
}

export interface FounderExperience {
  id: string
  founderId: string
  companyName: string
  title: string
  industry?: string
  companySize?: string
  startDate: string
  endDate?: string
  isPrimary?: boolean
  location?: string
}

export interface FounderEducation {
  id: string
  schoolName: string
  degrees: string[]
  majors: string[]
  startDate?: string
  endDate?: string
  location?: string
}

export interface FounderSkill {
  skill: string
}

export interface FounderProfile {
  id: string
  network: string
  url: string
}

export interface FounderData {
  id: string
  founderId?: string
  fullName: string
  firstName?: string
  lastName?: string
  currentJobTitle?: string
  currentJobCompany?: string
  email?: string
  title?: string
  bio?: string
  location?: string
  locationCountry?: string
  country?: string
  city?: string
  linkedinUrl?: string
  twitterUrl?: string
  githubUrl?: string
  confidenceScore?: number
  serialFounder?: boolean
  orgId?: string
  companyId?: string
  source?: string
  enrichmentDate?: string
  s3RawDataKey?: string
  createdAt?: string
  updatedAt?: string
}

export interface EnrichedFounder {
  founder: FounderData
  experiences: FounderExperience[]
  education: FounderEducation[]
  skills: FounderSkill[]
  profiles: FounderProfile[]
  signals: FounderSignals | null
}

export interface FounderAnalysisResponse {
  founders: EnrichedFounder[]
}

export interface PipelineTriggerRequest {
  companyId: string
  orgId: string
  companyName: string
  domain?: string
  formData?: {
    founderName?: string
    founderLinkedin?: string
  }
  pipelineTypes: string[]
  priority: 'low' | 'normal' | 'high'
}

export interface PipelineTriggerResponse {
  success: boolean
  message: string
  jobIds?: Record<string, string>
}

export const FounderAPI = {
  /**
   * Get enriched founder data for a deal
   */
  async getDealFounders(dealId: string): Promise<FounderAnalysisResponse> {
    const response = await apiClient.get(`/deals/${dealId}/founders`)
    return response.data
  },

  /**
   * Trigger founder enrichment pipeline
   */
  async triggerEnrichment(request: PipelineTriggerRequest): Promise<PipelineTriggerResponse> {
    const response = await apiClient.post('/pipelines/trigger', request)
    return response.data
  },

  /**
   * Generate AI summary for founders (placeholder for future implementation)
   */
  async generateFounderSummary(dealId: string): Promise<{ summary: string }> {
    // This would call an AI service to generate a team summary
    // For now, return a placeholder
    return {
      summary: "AI-generated team summary will be available soon."
    }
  }
}
