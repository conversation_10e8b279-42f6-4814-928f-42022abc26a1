/**
 * Sample data for testing the Founder Analysis interface
 * This shows the expected data structure from the backend API
 */

import { FounderAnalysisResponse } from './founder-api'

export const sampleFounderAnalysisResponse: FounderAnalysisResponse = {
  founders: [
    {
      founder: {
        fullName: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        title: "Senior Data Engineer",
        bio: "Experienced data engineer with expertise in machine learning and scalable systems",
        location: "Berlin, Germany",
        country: "Germany",
        city: "Berlin",
        linkedinUrl: "https://linkedin.com/in/prasanna-saravanan",
        twitterUrl: "https://twitter.com/prasanna_dev",
        githubUrl: "https://github.com/prasanna-dev",
        confidenceScore: 0.92,
        serialFounder: false
      },
      experiences: [
        {
          id: "exp_1",
          companyName: "Razor Group",
          position: "Senior Data Engineer",
          startDate: "2022-01-15",
          endDate: "2024-03-01",
          location: "Berlin, Germany",
          industry: "E-commerce Technology",
          companySize: "500-1000 employees"
        },
        {
          id: "exp_2",
          companyName: "DataTech Solutions",
          position: "Machine Learning Engineer",
          startDate: "2020-06-01",
          endDate: "2021-12-31",
          location: "Munich, Germany",
          industry: "AI/ML Consulting",
          companySize: "50-100 employees"
        },
        {
          id: "exp_3",
          companyName: "TechStart GmbH",
          position: "Software Developer",
          startDate: "2018-09-01",
          endDate: "2020-05-31",
          location: "Hamburg, Germany",
          industry: "Software Development",
          companySize: "10-50 employees"
        }
      ],
      education: [
        {
          id: "edu_1",
          schoolName: "Technical University of Munich",
          degrees: ["Master of Science"],
          majors: ["Computer Science"],
          startDate: "2016-09-01",
          endDate: "2018-07-31",
          location: "Munich, Germany"
        },
        {
          id: "edu_2",
          schoolName: "Indian Institute of Technology Delhi",
          degrees: ["Bachelor of Technology"],
          majors: ["Computer Science and Engineering"],
          startDate: "2012-08-01",
          endDate: "2016-05-31",
          location: "New Delhi, India"
        }
      ],
      skills: [
        { skill: "Python" },
        { skill: "Machine Learning" },
        { skill: "TensorFlow" },
        { skill: "PyTorch" },
        { skill: "AWS" },
        { skill: "Docker" },
        { skill: "Kubernetes" },
        { skill: "Apache Spark" },
        { skill: "SQL" },
        { skill: "PostgreSQL" },
        { skill: "MongoDB" },
        { skill: "Redis" },
        { skill: "Apache Kafka" },
        { skill: "Data Engineering" },
        { skill: "ETL Pipelines" },
        { skill: "Big Data" },
        { skill: "Analytics" },
        { skill: "Statistical Analysis" },
        { skill: "Data Visualization" },
        { skill: "Team Leadership" },
        { skill: "Agile Methodology" },
        { skill: "System Architecture" },
        { skill: "API Development" },
        { skill: "Microservices" },
        { skill: "DevOps" }
      ],
      profiles: [
        {
          id: "profile_1",
          network: "LinkedIn",
          url: "https://linkedin.com/in/prasanna-saravanan"
        },
        {
          id: "profile_2",
          network: "GitHub",
          url: "https://github.com/prasanna-dev"
        },
        {
          id: "profile_3",
          network: "Twitter",
          url: "https://twitter.com/prasanna_dev"
        }
      ],
      signals: {
        score: 78,
        tags: ["Experienced", "Technical Leader", "Data-Driven", "International Experience"],
        strengths: {
          items: [
            "Strong technical background with 6+ years of experience",
            "Proven track record in scaling data systems",
            "International education from top-tier institutions",
            "Leadership experience managing technical teams",
            "Deep expertise in machine learning and data engineering",
            "Experience with modern cloud technologies and DevOps"
          ]
        },
        risks: {
          items: [
            "Limited entrepreneurial experience",
            "No previous startup founding experience",
            "Primarily technical background, may need business co-founder",
            "No fundraising track record"
          ]
        },
        skillProfile: {
          tech: 9,
          product: 6,
          business: 5,
          operations: 7,
          fundraising: 3
        }
      }
    },
    {
      founder: {
        fullName: "Sarah Chen",
        email: "<EMAIL>",
        title: "Product Manager",
        bio: "Product leader with experience building consumer and B2B products",
        location: "San Francisco, CA",
        country: "United States",
        city: "San Francisco",
        linkedinUrl: "https://linkedin.com/in/sarah-chen-pm",
        confidenceScore: 0.88,
        serialFounder: true
      },
      experiences: [
        {
          id: "exp_4",
          companyName: "Meta",
          position: "Senior Product Manager",
          startDate: "2021-03-01",
          location: "Menlo Park, CA",
          industry: "Social Media Technology",
          companySize: "10000+ employees"
        },
        {
          id: "exp_5",
          companyName: "Stripe",
          position: "Product Manager",
          startDate: "2019-01-15",
          endDate: "2021-02-28",
          location: "San Francisco, CA",
          industry: "Fintech",
          companySize: "1000-5000 employees"
        }
      ],
      education: [
        {
          id: "edu_3",
          schoolName: "Stanford University",
          degrees: ["Master of Business Administration"],
          majors: ["Technology Management"],
          startDate: "2017-09-01",
          endDate: "2019-06-15",
          location: "Stanford, CA"
        }
      ],
      skills: [
        { skill: "Product Management" },
        { skill: "Product Strategy" },
        { skill: "User Experience Design" },
        { skill: "A/B Testing" },
        { skill: "Analytics" },
        { skill: "Go-to-Market Strategy" },
        { skill: "Agile Development" },
        { skill: "Stakeholder Management" },
        { skill: "Business Strategy" },
        { skill: "Fundraising" },
        { skill: "Team Leadership" }
      ],
      profiles: [
        {
          id: "profile_4",
          network: "LinkedIn",
          url: "https://linkedin.com/in/sarah-chen-pm"
        }
      ],
      signals: {
        score: 85,
        tags: ["Serial Founder", "Product Expert", "Big Tech Experience", "MBA"],
        strengths: {
          items: [
            "Strong product management background at top tech companies",
            "MBA from Stanford with technology focus",
            "Previous startup founding experience",
            "Proven ability to scale products to millions of users",
            "Strong business acumen and strategic thinking"
          ]
        },
        risks: {
          items: [
            "Limited technical implementation experience",
            "May need strong technical co-founder",
            "Previous startup outcome unknown"
          ]
        },
        skillProfile: {
          tech: 4,
          product: 9,
          business: 8,
          operations: 7,
          fundraising: 8
        }
      }
    }
  ]
}
