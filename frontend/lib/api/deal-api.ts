/**
 * Deal API Integration
 * 
 * API client for managing deals with the backend
 */

import apiClient from "@/lib/api-client"
import {
  Deal,
  DealListResponse,
  DealCreateRequest,
  DealUpdateRequest,
  DealAssignRequest,
  DealStatusUpdateRequest,
  DealStatus,
  DealFilters,
  DealDashboardFilters,
  UserPreferences,
  UpdateUserPreferencesRequest,
  User
} from '@/lib/types/deal';
import { normalizeDealsIds, normalizeDealId } from '@/lib/utils/deal-id';
import type { ThesisScoring } from "@/lib/types/thesis-scoring"

export const DealAPI = {
  /**
   * List deals with optional filtering and pagination
   */
  async listDeals(
    skip: number = 0,
    limit: number = 100,
    filters?: DealFilters & {
      status?: string[];
      assigned_to_me?: boolean;
      created_at_start?: string;
      created_at_end?: string;
      sort_by?: string;
      sort_order?: string;
      favourites_only?: boolean;
    }
  ): Promise<DealListResponse> {
    console.log('Fetching deals with filters:', filters);
    
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    // Add filters to params
    if (filters?.status) {
      if (Array.isArray(filters.status)) {
        params.append('status', filters.status.join(','));
      } else {
        params.append('status', filters.status);
      }
    }
    if (filters?.stage) {
      params.append('stage', filters.stage);
    }
    if (filters?.sector) {
      params.append('sector', filters.sector);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.tags && filters.tags.length > 0) {
      params.append('tags', filters.tags.join(','));
    }
    if (filters?.assigned_to_me !== undefined) {
      params.append('assigned_to_me', filters.assigned_to_me.toString());
    }
    if (filters?.created_at_start) {
      params.append('created_at_start', filters.created_at_start);
    }
    if (filters?.created_at_end) {
      params.append('created_at_end', filters.created_at_end);
    }
    if (filters?.sort_by) {
      params.append('sort_by', filters.sort_by);
    }
    if (filters?.sort_order) {
      params.append('sort_order', filters.sort_order);
    }
    if (filters?.favourites_only !== undefined) {
      params.append('favourites_only', filters.favourites_only.toString());
    }
    
    const response = await apiClient.get(`/deals?${params.toString()}`);
    console.log('Deals fetched:', response.data);

    // Normalize deal IDs for frontend use
    if (response.data.deals) {
      response.data.deals = normalizeDealsIds(response.data.deals);
    }

    return response.data;
  },

  /**
   * Get a single deal by ID
   */
  async getDeal(dealId: string): Promise<Deal> {
    console.log(`Fetching deal ${dealId}`);
    const response = await apiClient.get(`/deals/${dealId}`);
    console.log('Deal fetched:', response.data);

    // Normalize deal ID for frontend use
    return normalizeDealId(response.data) as Deal;
  },

  /**
   * Create a new deal
   */
  async createDeal(dealData: DealCreateRequest): Promise<Deal> {
    console.log('Creating deal with data:', dealData);
    const response = await apiClient.post('/deals', dealData);
    console.log('Deal created:', response.data);
    return response.data;
  },

  /**
   * Update an existing deal
   */
  async updateDeal(dealId: string, dealData: DealUpdateRequest): Promise<Deal> {
    console.log(`Updating deal ${dealId} with data:`, dealData);
    const response = await apiClient.put(`/deals/${dealId}`, dealData);
    console.log('Deal updated:', response.data);
    return response.data;
  },

  /**
   * Delete a deal
   */
  async deleteDeal(dealId: string): Promise<{ success: boolean }> {
    console.log(`Deleting deal ${dealId}`);
    const response = await apiClient.delete(`/deals/${dealId}`);
    console.log('Deal deleted:', response.data);
    return response.data;
  },

  /**
   * Get deals by form ID
   */
  async getDealsByForm(
    formId: string,
    skip: number = 0,
    limit: number = 100
  ): Promise<DealListResponse> {
    console.log(`Fetching deals for form ${formId}`);
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    const response = await apiClient.get(`/deals/by_form/${formId}?${params.toString()}`);
    console.log('Form deals fetched:', response.data);
    return response.data;
  },

  /**
   * Search deals with advanced filters
   */
  async searchDeals(
    query: string,
    filters?: any,
    skip: number = 0,
    limit: number = 100
  ): Promise<DealListResponse> {
    console.log('Searching deals with query:', query);
    const searchData = {
      query,
      filters,
      skip,
      limit
    };
    
    const response = await apiClient.post('/deals/search', searchData);
    console.log('Search results:', response.data);
    return response.data;
  },

  /**
   * Add timeline event to deal
   */
  async addTimelineEvent(
    dealId: string,
    event: string,
    notes?: string
  ): Promise<Deal> {
    console.log(`Adding timeline event to deal ${dealId}`);
    const eventData = {
      event,
      notes
    };
    
    const response = await apiClient.post(`/deals/${dealId}/timeline`, eventData);
    console.log('Timeline event added:', response.data);
    return response.data;
  },

  /**
   * Update deal notes
   */
  async updateDealNotes(dealId: string, notes: string): Promise<Deal> {
    console.log(`Updating notes for deal ${dealId}`);
    const response = await apiClient.put(`/deals/${dealId}/notes`, { notes });
    console.log('Deal notes updated:', response.data);
    return response.data;
  },

  /**
   * Get full analysis for a deal
   */
  async getFullAnalysis(dealId: string): Promise<any> {
    console.log(`Getting full analysis for deal ${dealId}`);
    const response = await apiClient.get(`/deals/${dealId}/full-analysis`);
    console.log('Full analysis fetched:', response.data);
    return response.data;
  },

  /**
   * Override a score for a deal
   */
  async overrideScore(
    dealId: string,
    signalType: string,
    newScore: number,
    reason: string
  ): Promise<any> {
    console.log(`Overriding score for deal ${dealId}`);
    const overrideData = {
      signal_type: signalType,
      new_score: newScore,
      reason
    };

    const response = await apiClient.post(`/deals/${dealId}/score-override`, overrideData);
    console.log('Score override applied:', response.data);
    return response.data;
  },

  async getThesisScoring(dealId: string): Promise<ThesisScoring> {
    const response = await apiClient.get(`/api/v1/deals/${dealId}/thesis-scoring`)
    return response.data
  },

  /**
   * Assign a user to a deal
   */
  async assignUserToDeal(dealId: string, userId: string): Promise<Deal> {
    console.log(`Assigning user ${userId} to deal ${dealId}`);
    const assignData: DealAssignRequest = {
      user_ids: [userId]
    };

    const response = await apiClient.patch(`/deals/${dealId}/assign`, assignData);
    console.log('User assigned to deal:', response.data);
    return normalizeDealId(response.data) as Deal;
  },

  /**
   * Unassign all users from a deal
   */
  async unassignAllUsers(dealId: string): Promise<Deal> {
    console.log(`Unassigning all users from deal ${dealId}`);
    const assignData: DealAssignRequest = {
      user_ids: []
    };

    const response = await apiClient.patch(`/deals/${dealId}/assign`, assignData);
    console.log('Users unassigned from deal:', response.data);
    return normalizeDealId(response.data) as Deal;
  },

  /**
   * Update deal status with optional note
   */
  async updateDealStatus(
    dealId: string,
    status: DealStatus,
    note?: string
  ): Promise<Deal> {
    console.log(`Updating deal ${dealId} status to ${status}`);
    const statusData: DealStatusUpdateRequest = {
      status,
      note
    };

    const response = await apiClient.patch(`/deals/${dealId}/status`, statusData);
    console.log('Deal status updated:', response.data);
    return normalizeDealId(response.data) as Deal;
  },

  /**
   * Get organization users for assignment
   */
  async getOrgUsers(): Promise<User[]> {
    console.log('Fetching organization members');
    const response = await apiClient.get('/settings/members');
    console.log('Organization members fetched:', response.data);
    return response.data;
  },

  /**
   * Get user's deal dashboard preferences
   */
  async getDealPreferences(): Promise<UserPreferences> {
    console.log('Fetching deal preferences');
    const response = await apiClient.get('/deals/preferences');
    console.log('Preferences fetched:', response.data);
    return response.data;
  },

  /**
   * Update user's deal dashboard preferences
   */
  async updateDealPreferences(preferences: UpdateUserPreferencesRequest): Promise<{ message: string; preferences: any }> {
    console.log('Updating deal preferences:', preferences);
    const response = await apiClient.patch('/deals/preferences', preferences);
    console.log('Preferences updated:', response.data);
    return response.data;
  },

  /**
   * Mark a deal as favourite
   */
  async favouriteDeal(dealId: string): Promise<Deal> {
    const response = await apiClient.post(`/deals/${dealId}/favourite`);
    return response.data;
  },

  /**
   * Remove a deal from favourites
   */
  async unfavouriteDeal(dealId: string): Promise<Deal> {
    const response = await apiClient.delete(`/deals/${dealId}/favourite`);
    return response.data;
  }
};

export default DealAPI;
