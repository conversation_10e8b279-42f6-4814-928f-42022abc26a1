/**
 * API client for handling pitch deck uploads
 */

import { API_BASE_URL, createAuthHeaders } from "./auth-api";

export interface PitchUploadRequest {
  type: "deck" | "one_pager";
  filename: string;
  content_type: string;
  file_size: number;
}

export interface PitchUploadResponse {
  temp_id: string;
  presigned_url: string;
  s3_key: string;
  status: string;
  expires_in: number;
}

export interface ConfirmPitchUploadRequest {
  temp_id: string;
}

export interface ConfirmPitchUploadResponse {
  temp_id: string;
  status: string;
  message: string;
}

export class PitchUploadAPI {
  /**
   * Generate presigned URL for pitch upload
   */
  static async generatePitchUploadUrl(
    request: PitchUploadRequest,
    token: string,
    orgId: string
  ): Promise<PitchUploadResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/pitch`, {
        method: 'POST',
        headers: createAuthHeaders(token, orgId),
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to generate pitch upload URL');
      }

      return response.json();
    } catch (error) {
      console.error('Generate pitch upload URL error:', error);
      throw error;
    }
  }

  /**
   * Upload file to S3 using presigned URL
   */
  static async uploadPitchFile(
    file: File,
    presignedUrl: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      xhr.open('PUT', presignedUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.setRequestHeader('Content-Length', file.size.toString());
      xhr.send(file);
    });
  }

  /**
   * Confirm pitch upload and trigger processing
   */
  static async confirmPitchUpload(
    request: ConfirmPitchUploadRequest,
    token: string,
    orgId: string
  ): Promise<ConfirmPitchUploadResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/pitch/confirm`, {
        method: 'POST',
        headers: createAuthHeaders(token, orgId),
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to confirm pitch upload');
      }

      return response.json();
    } catch (error) {
      console.error('Confirm pitch upload error:', error);
      throw error;
    }
  }

  /**
   * Complete pitch upload process (generate URL, upload, confirm)
   */
  static async uploadPitchComplete(
    file: File,
    token: string,
    orgId: string,
    onProgress?: (progress: number, status: string) => void
  ): Promise<ConfirmPitchUploadResponse> {
    try {
      // Step 1: Generate presigned upload URL
      onProgress?.(0, 'preparing');
      
      const uploadRequest: PitchUploadRequest = {
        type: 'deck', // Default to deck, could be made configurable
        filename: file.name,
        content_type: file.type,
        file_size: file.size
      };

      const uploadResponse = await this.generatePitchUploadUrl(uploadRequest, token, orgId);

      // Step 2: Upload file to S3
      onProgress?.(0, 'uploading');
      await this.uploadPitchFile(
        file,
        uploadResponse.presigned_url,
        (progress) => {
          onProgress?.(progress, 'uploading');
        }
      );

      // Step 3: Confirm upload
      onProgress?.(100, 'processing');
      const confirmResponse = await this.confirmPitchUpload(
        { temp_id: uploadResponse.temp_id },
        token,
        orgId
      );

      onProgress?.(100, 'completed');
      return confirmResponse;

    } catch (error) {
      onProgress?.(0, 'error');
      throw error;
    }
  }
} 