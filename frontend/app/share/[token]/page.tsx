"use client"

import { use<PERSON>arams } from 'next/navigation'
import { motion } from 'framer-motion'
import { AlertCircle, Star } from 'lucide-react'

import { SharedFormRenderer } from '@/components/core/form-share/shared-form-renderer'
import { ShareErrorBoundary } from '@/components/core/form-share/share-error-boundary'
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context'
import { cn } from '@/lib/utils'
import { visualRetreat, mobileRetreat } from '@/lib/utils/responsive'

// Debug component to test visibility logic
function VisibilityDebugger() {
  if (process.env.NODE_ENV !== 'development') return null;
  
  // Import here to avoid module resolution issues
  const { evaluateVisibility } = require('@/lib/utils/form-logic');
  
  // Test the exact case from the user
  const testAnswers = {
    "6847baa655bce11ad7dcd171": true, // Boolean value from form
  };
  
  const testCondition = {
    "_id": "685a579e5347f2273d99d6d4",
    "operator": "and" as const,
    "conditions": [
      {
        "question_id": "6847baa655bce11ad7dcd171",
        "value": "true", // String value from condition
        "operator": "=="
      }
    ]
  };
  
  // Run the actual evaluation using the correct format (just answers object)
  const result = evaluateVisibility(testCondition, testAnswers);
  
  return (
    <div className="fixed right-4 top-4 z-50 max-w-md rounded-lg border border-yellow-300 bg-yellow-100 p-4 text-xs">
      <h4 className="mb-2 font-bold text-yellow-800">🔬 Visibility Test Results</h4>
      
      <div className="space-y-2 text-yellow-700">
        <div><strong>Question:</strong> "Looking to raise" (6847baa655bce11ad7dcd171)</div>
        <div><strong>Actual Value:</strong> {JSON.stringify(testAnswers["6847baa655bce11ad7dcd171"])} ({typeof testAnswers["6847baa655bce11ad7dcd171"]})</div>
        <div><strong>Expected Value:</strong> "{testCondition.conditions[0].value}" ({typeof testCondition.conditions[0].value})</div>
        <div><strong>Operator:</strong> {testCondition.conditions[0].operator}</div>
        <div className={`font-bold ${result ? 'text-green-600' : 'text-red-600'}`}>
          <strong>Should "Target Raise" Show:</strong> {result ? 'YES ✅' : 'NO ❌'}
        </div>
        
        <div className="mt-2 border-t border-yellow-300 pt-2">
          <div className="text-xs">
            <div><strong>Using:</strong> form-logic.ts evaluateVisibility</div>
            <div><strong>Boolean Handling:</strong> Enhanced equality check</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ShareFormPage() {
  const params = useParams()
  const token = params?.token as string

  // Premium invalid token state
  if (!token) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
        <div className={mobileRetreat.error.container}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "mx-auto max-w-md p-8 text-center")}
          >
            <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-full bg-red-50">
              <AlertCircle className="size-8 text-red-500" />
            </div>

            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Invalid Form Link
            </h1>
            <p className="mb-6 text-gray-600">
              This form sharing link appears to be invalid or malformed. Please check the URL and try again.
            </p>

            <div className="space-y-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => window.location.reload()}
                className="touch-target w-full rounded-xl bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
              >
                Reload Page
              </motion.button>

              <p className="text-sm text-gray-500">
                If this issue persists, please contact the organization that shared this form.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  // Premium form sharing experience
  return (
    <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
      <VisibilityDebugger />
      <ShareErrorBoundary>
        <PublicAuthProvider>
          <SharedFormRenderer token={token} />
        </PublicAuthProvider>
      </ShareErrorBoundary>

      {/* Premium footer */}
      <motion.footer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mt-16 border-t border-gray-200/50 py-8"
      >
        <div className="mx-auto max-w-4xl px-4 text-center md:px-6">
          <div className="mb-2 flex items-center justify-center gap-2 text-sm text-gray-500">
            <Star className="size-4" />
            <span>Powered by TractionX</span>
          </div>
          <p className="text-xs text-gray-400">
            Taking Private market investing to the next era
          </p>
        </div>
      </motion.footer>
    </div>
  )
}
